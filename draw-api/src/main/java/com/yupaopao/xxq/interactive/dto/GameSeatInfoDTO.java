package com.yupaopao.xxq.interactive.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/3 14:54
 */
@Data
public class GameSeatInfoDTO implements Serializable {

    /**
     * id
     */
    private Long seatId;

    /**
     * 座位编号 1-9
     */
    private Integer seatNo;

    /**
     * 直播间 Id
     */
    private Long liveRoomId;

    /**
     * 用户 Id
     */
    private Long uid;

    /**
     * 0主动上麦、1邀请上麦
     */
    private Integer type;

    /**
     * 麦位状态：0=空麦 1=占麦 2=已上麦
     */
    private Integer state;

    /**
     * 是否是主播
     */
    private Boolean isAnchor;

    /**
     * 意向游戏
     */
    private String intentGame;

}
