package com.yupaopao.xxq.interactive.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/***
 * <AUTHOR>
 **/
@Data
public class SaveInteractiveRecordRequest implements Serializable {

    private Long anchorUid;

    private String scene;

    private String subScene;

    private String outId;

    private List<Long> sponsorUids = new ArrayList<>();

    private List<Long> playerUids = new ArrayList<>();

    private Map<String, Object> extra = new HashMap<>();

    private Long startTime;

}
