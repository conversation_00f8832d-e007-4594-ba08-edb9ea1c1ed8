package com.yupaopao.xxq.interactive.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
public class GameStateInfoDTO implements Serializable {


    /**
     * 游戏场景
     */
    private  String scene;

    /**
     * 正在游戏的用户
     */
    private List<Long> uidList;

    /**
     * 游戏开始时间
     */
    private Date startTime;
}
