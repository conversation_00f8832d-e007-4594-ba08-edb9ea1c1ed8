package com.yupaopao.xxq.interactive.request;

import lombok.Data;

import java.io.Serializable;

@Data
public class OpenGameUser implements Serializable {

    private String uid;

    private Integer identity;

    private Integer seatIndex;

    private String nickName;

    private String avatar;

    /**
     * 准备状态
     * 0: 未准备
     * 1: 准备
     */
    private Integer readyStatus;

    private String ext;

}
