

package com.yupaopao.xxq.interactive.request;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


@Data
public class MultiGameStartRequest implements Serializable {

    @NotBlank
    @Description("游戏场景")
    private String gameScene;

    @NotNull
    @Description("房间id")
    private Long liveRoomId;

    @NotNull
    @Description("anchorUid")
    private Long anchorUid;

}
