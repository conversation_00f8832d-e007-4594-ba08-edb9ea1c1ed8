package com.yupaopao.xxq.interactive.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/3 14:52
 */
@Data
public class MultiGameConfigDTO implements Serializable {

    /**
     * 直播间 Id
     */
    private Long liveRoomId;

    /**
     * 主播 Id
     */
    private Long anchorUid;

    /**
     * 游戏场景
     */
    private String gameScene;

    /**
     * 游戏状态
     */
    @Deprecated
    private Integer gameState;

    private Integer newGameState;

    /**
     * 游戏配置
     */
    private SudGameConfigDTO sudGameConfig;

    /**
     * rtc 业务 id
     */
    private String rtcBizId;

    /**
     * rtc 房间 id
     */
    private String rtcRoomId;

}
