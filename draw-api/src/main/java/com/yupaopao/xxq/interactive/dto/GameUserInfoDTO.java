package com.yupaopao.xxq.interactive.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GameUserInfoDTO implements Serializable {

    private String playerId;
    private String nickname;
    private String avatar;
    // 系统分配麦位：-2  麦位下返回：-1， 玩家麦位从0开始
    private Integer seatIndex;
    /**
     * 0女1男
     */
    private Integer gender;

}
