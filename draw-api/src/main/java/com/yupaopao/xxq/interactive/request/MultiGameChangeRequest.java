
package com.yupaopao.xxq.interactive.request;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


@Data
public class MultiGameChangeRequest implements Serializable {

    @Description("原游戏场景")
    private String originScene;

    @NotBlank
    @Description("切换后游戏场景")
    private String targetScene;

    @NotNull
    @Description("房间id")
    private Long liveRoomId;

    @NotNull
    @Description("anchorUid")
    private Long anchorUid;

}
