package com.yupaopao.xxq.interactive.request;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/***
 * <AUTHOR>
 **/
@Data
public class InteractiveRecordPageRequest implements Serializable {

    private Long anchorUid;

    private String scene;

    private Date startTime;

    private Date endTime;

    private String anchor;

    private Integer limit;

}
