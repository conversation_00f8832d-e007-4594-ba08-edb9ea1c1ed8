package com.yupaopao.xxq.interactive.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 **/
@Data
public class AnchorInteractiveSubRecordDTO implements Serializable {

    /**
     * 场景
     */
    private String parentScene;

    private String outId;

    /**
     * 子场景
     */
    private String scene;

    /**
     * 参与人
     */
    private List<Long> playerUidList;

    /**
     * 扩展字段
     */
    private Map<String, Object> extra;

    /**
     * 开始时间
     */
    private Date createTime;

}
