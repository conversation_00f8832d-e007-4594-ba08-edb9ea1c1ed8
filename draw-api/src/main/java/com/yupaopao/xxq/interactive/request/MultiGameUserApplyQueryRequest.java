package com.yupaopao.xxq.interactive.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * create by jiaq<PERSON> on 2021/7/5
 *
 * <AUTHOR>
 **/
@Data
public class MultiGameUserApplyQueryRequest implements Serializable {

    /**
     * 房间id
     */
    @NotNull
    private Long liveRoomId;

    private Long uid;

    /**
     * 列表查询使用 数量
     */
    private Integer limit;

    /**
     * 列表查询使用 游标
     */
    private String anchor;

}
