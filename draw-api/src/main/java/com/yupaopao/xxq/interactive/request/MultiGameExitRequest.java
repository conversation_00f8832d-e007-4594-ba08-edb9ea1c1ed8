


package com.yupaopao.xxq.interactive.request;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


@Data
public class MultiGameExitRequest implements Serializable {


    @NotNull
    @Description("房间id")
    private Long liveRoomId;

    @NotNull
    @Description("uid")
    private Long uid;

    @Description("在房时长，单位：s， 不能传0，最少传1s")
    private Integer duration;

}
