package com.yupaopao.xxq.interactive.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * create by ji<PERSON><PERSON> on 2021/7/5
 *
 * <AUTHOR>
 **/
@Data
public class InteractiveGameApplyDTO implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 游戏场景
     */
    private String scene;

    /**
     * 业务线
     */
    private Integer appId;

    /**
     * 房间ID
     */
    private Long liveRoomId;

    /**
     * 主播ID
     */
    private Long anchorUid;

    /**
     * 游戏ID
     */
    private Long gameId;

    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 类型：1-主播邀请 ,2-用户申请；
     */
    private Integer type;

    /**
     * 游戏状态（0等待、1主动取消、2主播忽略、3直播下播清除 4 申请成功 ）
     */
    private Integer status;

    /**
     * 申请时间
     */
    private Date applyTime;
}
