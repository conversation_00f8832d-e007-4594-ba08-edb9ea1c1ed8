package com.yupaopao.xxq.interactive.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * create by jiaq<PERSON> on 2021/7/5
 *
 * <AUTHOR>
 **/
@Data
public class MultiGameInviteRequest implements Serializable {

    /**
     * 房间id
     */
    @NotNull
    private Long liveRoomId;

    /**
     * 主播 uid
     */
    @NotNull
    private Long anchorUid;

    private Integer appId;

    /**
     * 邀请用户 uid
     */
    @NotNull
    private Long targetUid;

}
