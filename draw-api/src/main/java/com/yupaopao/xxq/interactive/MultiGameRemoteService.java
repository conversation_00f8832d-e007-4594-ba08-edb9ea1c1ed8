package com.yupaopao.xxq.interactive;

import com.yupaopao.platform.common.annotation.executor.CommonExecutor;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.xxq.game.dto.AnchorSudGameStatusDto;
import com.yupaopao.xxq.game.dto.MatchGameDTO;
import com.yupaopao.xxq.game.request.AnchorSudGameStatusReq;
import com.yupaopao.xxq.interactive.dto.*;
import com.yupaopao.xxq.interactive.request.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 多人小游戏
 **/
public interface MultiGameRemoteService {
    /**
     * 游戏申请
     *
     * @param request
     * @return
     */
    @CommonExecutor(printParam = true, printResponse = true)
    Response<Boolean> apply(MultiGameApplyRequest request);

    /**
     * 游戏邀请
     *
     * @param request
     * @return
     */
    @CommonExecutor(printParam = true, printResponse = true)
    Response<Boolean> invite(MultiGameInviteRequest request);

    /**
     * 取消游戏申请
     *
     * @param request
     * @return
     */
    @CommonExecutor(printParam = true, printResponse = true)
    Response<Boolean> cancel(MultiGameApplyCancelRequest request);


    /**
     * 主播拒绝游戏申请
     *
     * @param request
     * @return
     */
    @CommonExecutor(printParam = true, printResponse = true)
    Response<Boolean> anchorDisagree(MultiGameApplyDisagreeRequest request);

    /**
     * 用户拒绝游戏邀请
     *
     * @param request
     * @return
     */
    @CommonExecutor(printParam = true, printResponse = true)
    Response<Boolean> userDisagree(MultiGameApplyDisagreeRequest request);

    /**
     * 主播同意用户申请
     */
    @CommonExecutor(printParam = true, printResponse = true)
    Response<Boolean> anchorAgreeApply(MultiGameApplyAgreeRequest request);


    /**
     * 用户同意主播邀请
     */
    @CommonExecutor(printParam = true, printResponse = true)
    Response<Boolean> userAgreeInvite(MultiGameInviteAgreeRequest request);


    /**
     * 用户从sud房坐上麦位，仅限sud房可用
     */
    @CommonExecutor(printParam = true, printResponse = true)
    Response<Boolean> seatDownFromGameRoom(MultiGameSeatDownRequest request);


    /**
     * 重连小游戏rtc
     */
    @CommonExecutor(printParam = true, printResponse = true)
    Response<MultiGameRTCConnectDTO> connectRTC(MultiGameConnectRTCRequest request);


    /**
     * 主播选择游戏
     */
    @CommonExecutor(printParam = true, printResponse = true)
    Response<Boolean> chooseGame(MultiGameChooseRequest request);


    /**
     * 主播开始游戏
     */
    @CommonExecutor(printParam = true, printResponse = true)
    Response<MultiGameStartDTO> startGame(MultiGameStartRequest request);


    /**
     * 主播开始一轮游戏
     */
    @CommonExecutor(printParam = true, printResponse = true)
    Response<Boolean> roundStart(MultiGameRoundStartRequest request);

    /**
     * 主播结束一轮游戏
     */
    @CommonExecutor(printParam = true, printResponse = true)
    Response<Boolean> roundEnd(MultiGameRoundEndRequest request);

    /**
     * 主播切换游戏
     */
    @CommonExecutor(printParam = true, printResponse = true)
    Response<MultiGameStartDTO> changeGame(MultiGameChangeRequest request);


    /**
     * 用户主动退出连麦
     */
    @CommonExecutor(printParam = true, printResponse = true)
    Response<Boolean> exit(MultiGameExitRequest request);

    /**
     * 主播把用户踢下麦
     */
    @CommonExecutor(printParam = true, printResponse = true)
    Response<Boolean> kick(MultiGameKickRequest request);


    /**
     * 结束游戏
     *
     * @param request
     * @return
     */
    @CommonExecutor(printParam = true, printResponse = true)
    Response<Boolean> end(MultiGameEndRequest request);

    /**
     * 结束游戏，不清理麦位，并切换游戏
     *
     * @param request
     * @return
     */
    @CommonExecutor(printParam = true, printResponse = true)
    Response<Boolean> endGameAndChange(MultiGameEndChangeRequest request);


    /**
     * 查询游戏申请信息
     *
     * @param request
     * @return
     */
    Response<MultiGameApplyDTO> queryApplyInfo(MultiGameApplyQueryRequest request);

    /**
     * 查询用户麦位信息
     *
     * @param request
     * @return
     */
    Response<GameSeatInfoDTO> querySeatInfo(MultiGameUserSeatQueryRequest request);

    /**
     * 用户查询报名列表
     *
     * @param request
     * @return
     */
    Response<MultiGameApplyPageList> queryApplyPageList(MultiGameApplyQueryRequest request);

    /**
     * 主播查询用户报名列表
     *
     * @param request
     * @return
     */
    Response<MultiGameUserApplyPageList> queryUserApplyList(MultiGameUserApplyQueryRequest request);

    /**
     * 主播查询邀请列表
     *
     * @param request
     * @return
     */
    Response<MultiGameAnchorInvitePageList> queryAnchorInviteList(MultiGameAnchorInviteQueryRequest request);

    /**
     * 查询游戏麦位信息
     *
     * @param request
     * @return
     */
    Response<MultiGameSeatInfoDTO> getGameSeatInfo(MultiGameSeatInfoRequest request);

    /**
     * 查询主播的 sud 游戏状态，用户中心用
     */
    @CommonExecutor(desc = "查询主播的 sud 游戏状态", printParam = true)
    Response<AnchorSudGameStatusDto> queryAnchorSudGameStatus(@NotNull AnchorSudGameStatusReq request);

    /**
     * 查询 sud 匹配游戏主播状态
     * @param request 参数
     * @return 返回
     */
    @CommonExecutor(desc = "查询 sud 匹配游戏主播状态", printParam = true)
    Response<List<SudMatchAnchorStatusDto>> queryBatchSudMatchAnchorStatus(BatchSudMatchAnchorStatusReq request);

    /**
     * 查询匹配游戏列表
     */
    @CommonExecutor(desc = "查询匹配游戏列表", printParam = true)
    Response<List<MatchGameDTO>> queryMatchGameList();

    /**
     * 查询匹配游戏列表（支持白名单过滤）
     *
     * @param uid 用户uid，用于白名单过滤
     */
    @CommonExecutor(desc = "查询匹配游戏列表（支持白名单过滤）", printParam = true)
    Response<List<MatchGameDTO>> queryMatchGameList(Long uid);

    /**
     * 查询热门的匹配游戏列表，最多两个
     */
    @CommonExecutor(desc = "查询热门的匹配游戏列表，最多两个", printParam = true)
    Response<List<MatchGameDTO>> popularList();

    /**
     * 查询sud游戏房曝光（匹配）开关
     */
    @CommonExecutor(desc = "查询sud游戏房曝光（匹配）开关", printParam = true)
    Response<Boolean> queryMatchSwitchInfo(GameMatchSwitchQueryReq request);

    /**
     * 更新sud游戏房曝光（匹配）开关
     */
    @CommonExecutor(desc = "更新sud游戏房曝光（匹配）开关", printParam = true, printResponse = true)
    Response<Boolean> matchSwitchChange(GameMatchSwitchReq request);

    /**
     * 在sud游戏房内的用户列表，不包含直播间
     */
    @CommonExecutor(desc = "在sud游戏房内的用户列表，不包含直播间", printParam = true)
    Response<List<MatchUserInfo>> matchUserList(MatchUserListReq request);

    /**
     * 查询sud用户所在房间的 exist 信息
     * @param sudExistReq 参数
     * @return 返回
     */
    @CommonExecutor(desc = "查询sud用户所在房间的 exist 信息", printParam = true)
    Response<SudUserExistInfo> getSudUserExist(SudExistReq sudExistReq);
}
