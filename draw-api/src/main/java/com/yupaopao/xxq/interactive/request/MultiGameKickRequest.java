


package com.yupaopao.xxq.interactive.request;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


@Data
public class MultiGameKickRequest implements Serializable {


    @NotNull
    @Description("房间id")
    private Long liveRoomId;

    @NotNull
    @Description("主播uid")
    private Long anchorUid;

    @NotNull
    @Description("uid")
    private Long targetUid;

}
