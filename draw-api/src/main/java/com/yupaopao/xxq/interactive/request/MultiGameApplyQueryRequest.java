package com.yupaopao.xxq.interactive.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * create by ji<PERSON><PERSON> on 2021/7/5
 *
 * <AUTHOR>
 **/
@Data
public class MultiGameApplyQueryRequest implements Serializable {

    /**
     * 房间id
     */
    @NotNull
    private Long liveRoomId;

    /**
     * 游戏场景
     */
    @NotBlank
    private String scene;

    private Long uid;

    /**
     * @see com.yupaopao.xxq.interactive.constant.GameTypeEnum
     */
    private Integer type;

    /**
     * 列表查询使用 数量
     */
    private Integer limit;

    /**
     * 列表查询使用 游标
     */
    private String anchor;

}
