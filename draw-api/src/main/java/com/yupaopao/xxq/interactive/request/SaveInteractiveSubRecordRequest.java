package com.yupaopao.xxq.interactive.request;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/***
 * <AUTHOR>
 **/
@Data
public class SaveInteractiveSubRecordRequest implements Serializable {

    private static final long serialVersionUID = 8211223765316201685L;

    private Long anchorUid;

    private String parentScene;

    private String scene;

    private String outId;

    /**
     * 参与人
     */
    private List<Long> playerUidList;

    private Map<String, Object> extra = new HashMap<>();

    private Long startTime;

}
