package com.yupaopao.xxq.interactive.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 **/
@Data
public class AnchorInteractiveRecordDTO implements Serializable {

    private String outId;
    /**
     * 场景
     */
    private String scene;

    /**
     * 子场景
     */
    private String subScene;

    private List<String> subSceneList;

    /**
     * 赞助用户列表
     */
    private List<Long> sponsorUids;

    /**
     * 参与用户列表
     */
    private List<Long> playerUids;

    /**
     * 扩展字段
     */
    private Map<String, Object> extra;

    /**
     * 开始时间
     */
    private Date createTime;

}
