package com.yupaopao.xxq.interactive.request;

import lombok.Data;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * create by jiaq<PERSON> on 2021/7/5
 *
 * <AUTHOR>
 **/
@Data
public class MultiGameApplyRequest implements Serializable {
    /**
     * 房间id
     */
    @NotNull
    private Long liveRoomId;

    /**
     * 游戏场景
     */
    @NotNull
    private String scene;

    @NotNull
    private Long uid;

    @NotNull
    private Integer appId;

    @Nullable
    private Integer type;
}
