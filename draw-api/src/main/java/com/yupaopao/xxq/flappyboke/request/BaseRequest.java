package com.yupaopao.xxq.flappyboke.request;

import java.io.Serializable;

public class BaseRequest implements Serializable {

    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 直播间ID
     */
    private Long liveRoomId;

    /**
     * 场景枚举(给每个场景分配的key)
     */
    private String scene;

    public Long getLiveRoomId() {
        return liveRoomId;
    }

    public void setLiveRoomId(Long liveRoomId) {
        this.liveRoomId = liveRoomId;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    @Override
    public String toString() {
        return "BaseRequest{" +
                "uid=" + uid +
                ", liveRoomId=" + liveRoomId +
                ", scene='" + scene + '\'' +
                '}';
    }
}
