package com.yupaopao.xxq.flappyboke.dto;

import java.io.Serializable;
import java.util.Map;

public class LabelDTO implements Serializable {

    /**
     * 类型
     */
    private Integer type;

    /**
     * 权重
     */
    private Integer weight;

    private Map<String, Object> param;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getWeight() {
        return weight;
    }

    public void setWeight(Integer weight) {
        this.weight = weight;
    }


    public Map<String, Object> getParam() {
        return param;
    }

    public void setParam(Map<String, Object> param) {
        this.param = param;
    }

    @Override
    public String toString() {
        return "LabelDTO{" +
                "type=" + type +
                ", weight=" + weight +
                ", param=" + param +
                '}';
    }
}
