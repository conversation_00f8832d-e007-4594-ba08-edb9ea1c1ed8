package com.yupaopao.xxq.flappyboke.dto;

import java.io.Serializable;

public class UserSupportDetailDTO implements Serializable {

    /**
     * 用户id
     */
    private Long uid;

    /**
     * 用户昵称
     */
    private String username;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 用户瓜分的收入
     */
    private Integer income;

    /**
     * 是否运气王 0 否 1是
     */
    private Integer luck;

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public Integer getIncome() {
        return income;
    }

    public void setIncome(Integer income) {
        this.income = income;
    }

    public Integer getLuck() {
        return luck;
    }

    public void setLuck(Integer luck) {
        this.luck = luck;
    }

    @Override
    public String toString() {
        return "UserSupportDetailDTO{" +
                "uid=" + uid +
                ", username='" + username + '\'' +
                ", avatar='" + avatar + '\'' +
                ", income=" + income +
                ", luck=" + luck +
                '}';
    }
}
