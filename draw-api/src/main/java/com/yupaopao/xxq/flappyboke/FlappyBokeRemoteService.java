package com.yupaopao.xxq.flappyboke;

import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.xxq.flappyboke.dto.FlappyWordDTO;
import com.yupaopao.xxq.flappyboke.dto.UserSupportDTO;
import com.yupaopao.xxq.flappyboke.dto.UserSupportDetailDTO;
import com.yupaopao.xxq.flappyboke.request.FlappyWordQueryRequest;
import com.yupaopao.xxq.flappyboke.request.SupportGameRequest;

import java.util.List;


public interface FlappyBokeRemoteService {

    /**
     * 助力游戏
     * @param request
     * @return
     */
    Response<Boolean> supportGame(SupportGameRequest request);


    /**
     * 查询助力
     * @param request
     * @return
     */
    Response<UserSupportDTO> querySupportList(SupportGameRequest request);


    /**
     * 随机获取推荐字
     *
     * @param request
     * @return
     */
    Response<List<FlappyWordDTO>> queryRandomFlappyWord(FlappyWordQueryRequest request);

    /**
     * 查询用户助力列表
     * @param request
     * @return
     */
    Response<List<UserSupportDetailDTO>> queryUserSupportList(SupportGameRequest request);

}
