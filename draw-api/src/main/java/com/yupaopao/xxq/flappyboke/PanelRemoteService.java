package com.yupaopao.xxq.flappyboke;

import com.yupaopao.platform.common.annotation.executor.CommonExecutor;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.xxq.flappyboke.dto.AnchorPanelDTO;
import com.yupaopao.xxq.flappyboke.dto.UserPanelDTO;
import com.yupaopao.xxq.flappyboke.request.BaseRequest;
import com.yupaopao.xxq.flappyboke.request.UserPanelRequest;

public interface PanelRemoteService {

    /**
     * 用户侧面板
     * @param request
     * @return
     */
    @CommonExecutor(desc = "用户侧面板")
    Response<UserPanelDTO> getUserPanel(UserPanelRequest request);

    /**
     * 主播侧面板
     * @param request
     * @return
     */
    @CommonExecutor(desc = "主播侧面板")
    Response<AnchorPanelDTO> getAnchorPanel(BaseRequest request);




}