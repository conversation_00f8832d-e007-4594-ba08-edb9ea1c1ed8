package com.yupaopao.xxq.flappyboke.dto;

import java.io.Serializable;
import java.util.List;

public class UserSupportDTO implements Serializable {

    /**
     * 瓜分到的收入
     */
    private Integer income = 0;

    /**
     * @see com.yupaopao.xxq.game.enums.FeeTypeEnum
     */
    private Integer type;

    /**
     * 是否有剩余机会 0 否 1 是
     */
    private Integer nextChance;

    private List<UserSupportDetailDTO> supportDetailDTOList;

    public Integer getIncome() {
        return income;
    }

    public void setIncome(Integer income) {
        this.income = income;
    }

    public List<UserSupportDetailDTO> getSupportDetailDTOList() {
        return supportDetailDTOList;
    }

    public void setSupportDetailDTOList(List<UserSupportDetailDTO> supportDetailDTOList) {
        this.supportDetailDTOList = supportDetailDTOList;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getNextChance() {
        return nextChance;
    }

    public void setNextChance(Integer nextChance) {
        this.nextChance = nextChance;
    }

    @Override
    public String toString() {
        return "UserSupportDTO{" +
                "income=" + income +
                ", type=" + type +
                ", nextChance=" + nextChance +
                ", supportDetailDTOList=" + supportDetailDTOList +
                '}';
    }
}
