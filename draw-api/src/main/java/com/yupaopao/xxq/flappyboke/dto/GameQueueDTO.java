package com.yupaopao.xxq.flappyboke.dto;

import java.io.Serializable;
import java.util.List;

public class GameQueueDTO implements Serializable {

    /**
     * 用户名
     */
    private String username;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 头像
     */
    private String avatar;

    /**
     * uid
     */
    private Long uid;

    /**
     * 标签
     */
    private List<LabelDTO> labelList;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public List<LabelDTO> getLabelList() {
        return labelList;
    }

    public void setLabelList(List<LabelDTO> labelList) {
        this.labelList = labelList;
    }

    @Override
    public String toString() {
        return "GameQueue{" +
                "username='" + username + '\'' +
                ", gender=" + gender +
                ", avatar='" + avatar + '\'' +
                ", uid=" + uid +
                ", labelList=" + labelList +
                '}';
    }
}
