package com.yupaopao.xxq.flappyboke.dto;

import com.yupaopao.xxq.game.dto.GameInfoDTO;
import com.yupaopao.xxq.game.dto.GameRuleDTO;

import java.io.Serializable;
import java.util.List;

public class PanelDTO implements Serializable {

    /**
     * 游戏标题
     */
    private GameInfoDTO gameInfoDTO;

    /**
     * 游戏规则
     */
    private GameRuleDTO gameRuleDTO;

    /**
     * 权限
     */
    private Boolean permission = Boolean.TRUE;

    /**
     * 排在前面的几个人
     */
    private List<GameQueueDTO> queueList;

    /**
     * 报名费
     */
    private Long fee;

    public GameInfoDTO getGameInfoDTO() {
        return gameInfoDTO;
    }

    public void setGameInfoDTO(GameInfoDTO gameInfoDTO) {
        this.gameInfoDTO = gameInfoDTO;
    }

    public GameRuleDTO getGameRuleDTO() {
        return gameRuleDTO;
    }

    public void setGameRuleDTO(GameRuleDTO gameRuleDTO) {
        this.gameRuleDTO = gameRuleDTO;
    }

    public Boolean getPermission() {
        return permission;
    }

    public void setPermission(Boolean permission) {
        this.permission = permission;
    }

    public List<GameQueueDTO> getQueueList() {
        return queueList;
    }

    public void setQueueList(List<GameQueueDTO> queueList) {
        this.queueList = queueList;
    }

    public Long getFee() {
        return fee;
    }

    public void setFee(Long fee) {
        this.fee = fee;
    }

    @Override
    public String toString() {
        return "PanelDTO{" +
                "gameInfoDTO=" + gameInfoDTO +
                ", gameRuleDTO=" + gameRuleDTO +
                ", permission=" + permission +
                ", queueList=" + queueList +
                ", fee=" + fee +
                '}';
    }
}
