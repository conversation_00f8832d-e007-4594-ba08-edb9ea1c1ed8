package com.yupaopao.xxq.flappyboke.request;

import com.yupaopao.xxq.game.request.EndRequest;

public class FlappyBokeEndRequest extends EndRequest {

    /**
     * 0-失败结束，1-成功结束
     */
    private Integer state;

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    @Override
    public String toString() {
        return "FlappyBokeEndRequest{" +
                "state=" + state +
                "} " + super.toString();
    }
}
