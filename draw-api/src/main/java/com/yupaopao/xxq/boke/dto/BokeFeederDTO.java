package com.yupaopao.xxq.boke.dto;

import java.io.Serializable;

public class BokeFeederDTO implements Serializable {
    private static final long serialVersionUID = -4124776829345696036L;

    private Long id;

    /**
     * 主播id
     */
    private Long uid;

    /**
     * 经验值
     */
    private Long exp;

    /**
     * 鱼粮数
     */
    private Integer food;

    /**
     * 便便数
     */
    private Integer shit;

    /**
     * 是否新饲养员
     */
    private Boolean newFeeder;

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Long getExp() {
        return exp;
    }

    public void setExp(Long exp) {
        this.exp = exp;
    }

    public Integer getFood() {
        return food;
    }

    public void setFood(Integer food) {
        this.food = food;
    }

    public Integer getShit() {
        return shit;
    }

    public void setShit(Integer shit) {
        this.shit = shit;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Boolean getNewFeeder() {
        return newFeeder;
    }

    public void setNewFeeder(Boolean newFeeder) {
        this.newFeeder = newFeeder;
    }
}
