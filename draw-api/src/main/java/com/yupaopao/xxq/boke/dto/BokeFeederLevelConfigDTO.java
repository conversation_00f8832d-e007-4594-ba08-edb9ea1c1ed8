package com.yupaopao.xxq.boke.dto;

import java.io.Serializable;

public class BokeFeederLevelConfigDTO implements Serializable {
    private static final long serialVersionUID = 199302017557139869L;


    /**
     * 等级
     */
    private Integer feederLevel;

    /**
     * 饲养员头衔
     */
    private String title;

    /**
     * 经验值
     */
    private Integer exp;

    /**
     * 收取最小百分比
     */
    private Integer gatherMinPercent;

    /**
     * 收取最大百分比
     */
    private Integer gatherMaxPercent;

    /**
     * 每日收取量限制
     */
    private Integer dailyLimit;

    private String icon;


    public Integer getFeederLevel() {
        return feederLevel;
    }

    public void setFeederLevel(Integer feederLevel) {
        this.feederLevel = feederLevel;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getExp() {
        return exp;
    }

    public void setExp(Integer exp) {
        this.exp = exp;
    }

    public Integer getGatherMinPercent() {
        return gatherMinPercent;
    }

    public void setGatherMinPercent(Integer gatherMinPercent) {
        this.gatherMinPercent = gatherMinPercent;
    }

    public Integer getGatherMaxPercent() {
        return gatherMaxPercent;
    }

    public void setGatherMaxPercent(Integer gatherMaxPercent) {
        this.gatherMaxPercent = gatherMaxPercent;
    }

    public Integer getDailyLimit() {
        return dailyLimit;
    }

    public void setDailyLimit(Integer dailyLimit) {
        this.dailyLimit = dailyLimit;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }
}
