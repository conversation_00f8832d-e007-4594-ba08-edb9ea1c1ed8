package com.yupaopao.xxq.boke.api;

import com.yupaopao.platform.common.annotation.executor.CommonExecutor;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.xxq.boke.dto.FeedBokeResultDTO;
import com.yupaopao.xxq.boke.request.ReceiveFansClubTaskRequest;
import com.yupaopao.xxq.boke.request.ReceiveTaskRequest;


/**
 * <AUTHOR>
 * @date 2020/8/14 6:53 下午
 */
public interface BokeTaskRemoteService {

    /**
     * 波克任务领取奖励
     *
     * @param request
     * @return
     */
    @CommonExecutor(desc = "波克任务领取奖励", printParam = true)
    Response<Boolean> receiveTask(ReceiveTaskRequest request);

    /**
     * 领取粉丝团任务
     *
     * @param request
     * @return
     */
    @CommonExecutor(desc = "领取粉丝团任务", printParam = true)
    Response<FeedBokeResultDTO> receiveFansClubTaskV2(ReceiveFansClubTaskRequest request);

    /**
     * 查询是否领取粉丝团任务
     *
     * @param liveRoomId
     * @param uid
     * @return
     */
    @CommonExecutor(desc = "查询是否领取粉丝团任务")
    Response<Boolean> hasReceiveFansClubTask(Long liveRoomId, Long uid);

    /**
     * 处理当天 转发直播间任务
     */
    @CommonExecutor(desc = "处理当天 转发直播间任务")
    Response<Boolean> handleForwardEvent(long uid);


    /**
     * 查询转发直播间任务 状态
     */
    @CommonExecutor(desc = "查询转发直播间任务 状态")
    Response<Integer> queryForwardTaskState(long uid);

    /**
     * 领取签到任务 奖励
     */
    @CommonExecutor(desc = "领取签到任务 奖励")
    Response<Integer> receiveRewardForSignedTask(long uid,int amount);

    /**
     * 查询签到任务 状态
     */
    @CommonExecutor(desc = "查询签到任务 状态")
    Response<Integer> querySignedTaskState(long uid);

}
