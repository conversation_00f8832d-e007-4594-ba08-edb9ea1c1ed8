package com.yupaopao.xxq.boke.dto;

import java.io.Serializable;
import java.util.Date;

public class BokeDTO implements Serializable {
    private static final long serialVersionUID = 4534625617799927482L;


    private Long id;


    /**
     * 房间id
     */
    private Long liveRoomId;

    /**
     * 主播id
     */
    private Long uid;

    /**
     * 经验值
     */
    private Long exp;

    /**
     * 产生便便时间
     */
    private Date produceShitTime;
    /**
     * 产生便便时间下限
     */
    private Date nextProduceLowerLimit;

    private Integer delayJob;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getLiveRoomId() {
        return liveRoomId;
    }

    public void setLiveRoomId(Long liveRoomId) {
        this.liveRoomId = liveRoomId;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Long getExp() {
        return exp;
    }

    public void setExp(Long exp) {
        this.exp = exp;
    }

    public Date getProduceShitTime() {
        return produceShitTime;
    }

    public void setProduceShitTime(Date produceShitTime) {
        this.produceShitTime = produceShitTime;
    }

    public Date getNextProduceLowerLimit() {
        return nextProduceLowerLimit;
    }

    public void setNextProduceLowerLimit(Date nextProduceLowerLimit) {
        this.nextProduceLowerLimit = nextProduceLowerLimit;
    }

    public Integer getDelayJob() {
        return delayJob;
    }

    public void setDelayJob(Integer delayJob) {
        this.delayJob = delayJob;
    }
}
