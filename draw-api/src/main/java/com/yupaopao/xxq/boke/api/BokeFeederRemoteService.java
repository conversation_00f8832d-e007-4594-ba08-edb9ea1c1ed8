package com.yupaopao.xxq.boke.api;

import com.yupaopao.platform.common.annotation.executor.CommonExecutor;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.xxq.boke.dto.BokeDTO;
import com.yupaopao.xxq.boke.dto.BokeFeederDTO;
import com.yupaopao.xxq.boke.dto.BokeFeederLevelConfigDTO;
import com.yupaopao.xxq.boke.dto.FeedBokeResultDTO;
import com.yupaopao.xxq.boke.dto.ShitAutoTransferDTO;
import com.yupaopao.xxq.boke.request.FeedBokeRequest;
import com.yupaopao.xxq.boke.request.ReceiveShitRequest;
import com.yupaopao.xxq.boke.request.TradeShitRequest;
import com.yupaopao.xxq.boke.resp.ReceiveShitResponse;
import java.util.List;

public interface BokeFeederRemoteService {

    @CommonExecutor(desc = "查询boke 饲养师等级配置表")
    Response<List<BokeFeederLevelConfigDTO>> getAllLevelConfig();

    @CommonExecutor(desc = "根据uid 查询饲养师")
    Response<BokeFeederDTO> queryByUid(Long uid);

    @CommonExecutor(desc = "根据uid 查询饲养师")
    Response<List<BokeFeederDTO>> batchQueryByUid(List<Long> uidList);

    @CommonExecutor(desc = "喂养boke")
    Response<FeedBokeResultDTO> feedBokeV2(FeedBokeRequest request);

    @CommonExecutor(desc = "查询 自动兑换星星情况")
    Response<ShitAutoTransferDTO> queryAutoTransfer(long uid);

    @CommonExecutor(desc = "查询今日 喂养数")
    Response<Integer> queryTodayFeedAmount(long uid);
}
