package com.yupaopao.xxq.boke.request;

import java.io.Serializable;

public class FeedBokeRequest implements Serializable {
    private static final long serialVersionUID = -6159428832949999724L;
    private Long feederUid;
    private Long bokeId;
    private Integer feedAmount;
    private Long anchorUid;

    public Long getAnchorUid() {
        return anchorUid;
    }

    public void setAnchorUid(Long anchorUid) {
        this.anchorUid = anchorUid;
    }

    public Long getFeederUid() {
        return feederUid;
    }

    public void setFeederUid(Long feederUid) {
        this.feederUid = feederUid;
    }

    public Long getBokeId() {
        return bokeId;
    }

    public void setBokeId(Long bokeId) {
        this.bokeId = bokeId;
    }

    public Integer getFeedAmount() {
        return feedAmount;
    }

    public void setFeedAmount(Integer feedAmount) {
        this.feedAmount = feedAmount;
    }
}
