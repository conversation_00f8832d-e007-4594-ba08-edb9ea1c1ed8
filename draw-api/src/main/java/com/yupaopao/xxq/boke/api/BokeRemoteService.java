package com.yupaopao.xxq.boke.api;

import com.yupaopao.platform.common.annotation.executor.CommonExecutor;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.xxq.boke.dto.BokeDTO;
import java.util.List;

public interface BokeRemoteService {

    @CommonExecutor(desc = "根据uid 查询boke")
    Response<BokeDTO> getBokeInfoByUid(Long uid);

    @CommonExecutor(desc = "根据uids 批量 查询现有boke 不自动初始化")
    Response<List<BokeDTO>> batchGetBokeByUids(List<Long> uids);
}
