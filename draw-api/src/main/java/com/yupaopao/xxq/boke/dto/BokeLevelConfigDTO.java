package com.yupaopao.xxq.boke.dto;

import java.io.Serializable;

public class BokeLevelConfigDTO implements Serializable {
    private static final long serialVersionUID = 8608174531323922856L;

    /**
     * 等级
     */
    private Integer level;

    /**
     * 所需总经验值
     */
    private Long expTotal;

    /**
     * 产生便便最小值
     */
    private Integer shitMin;

    /**
     * 产生便便最大值
     */
    private Integer shitMax;

    private String icon;

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Long getExpTotal() {
        return expTotal;
    }

    public void setExpTotal(Long expTotal) {
        this.expTotal = expTotal;
    }

    public Integer getShitMin() {
        return shitMin;
    }

    public void setShitMin(Integer shitMin) {
        this.shitMin = shitMin;
    }

    public Integer getShitMax() {
        return shitMax;
    }

    public void setShitMax(Integer shitMax) {
        this.shitMax = shitMax;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }
}
