package com.yupaopao.xxq.boke.api;

import com.yupaopao.platform.common.annotation.executor.CommonExecutor;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.xxq.boke.dto.BokeInviteRecordDTO;

public interface BokeInviteRecordRemoteService {


    @CommonExecutor(desc = "主播邀请观众喂养boke")
    Response<Boolean> invite(Long uid);
    @CommonExecutor(desc = "查询主播最新一次邀请")
    Response<BokeInviteRecordDTO> getLatestInvite(Long uid);
}
