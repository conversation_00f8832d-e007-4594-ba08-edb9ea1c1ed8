package com.yupaopao.xxq.boke.dto;

import java.io.Serializable;
import java.util.Date;

public class BokeShitDTO implements Serializable {
    private static final long serialVersionUID = -444891452134859450L;

    private Long id;

    private Long bokeId;

    private Integer amount;

    private Integer remainAmount;

    private Date expireTime;

    private Boolean canReceive;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBokeId() {
        return bokeId;
    }

    public void setBokeId(Long bokeId) {
        this.bokeId = bokeId;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public Boolean getCanReceive() {
        return canReceive;
    }

    public void setCanReceive(Boolean canReceive) {
        this.canReceive = canReceive;
    }

    public Integer getRemainAmount() {
        return remainAmount;
    }

    public void setRemainAmount(Integer remainAmount) {
        this.remainAmount = remainAmount;
    }
}
