package com.yupaopao.xxq.boke.api;

import com.yupaopao.platform.common.annotation.executor.CommonExecutor;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.xxq.boke.dto.BokeShitDTO;
import com.yupaopao.xxq.boke.request.TradeShitRequest;
import java.util.List;

public interface BokeShitRemoteService {
    @CommonExecutor(desc = "根据boke id 查询最新的便便list")
    Response<List<BokeShitDTO>> getLatestShitList(Long bokeId,Long viewerUid);
}
