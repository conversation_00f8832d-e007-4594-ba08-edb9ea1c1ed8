package com.yupaopao.xxq.boke.resp;

import java.io.Serializable;

public class ReceiveShitResponse implements Serializable {
    private static final long serialVersionUID = -8030913535070268440L;

    /**
     * 便便数
     */
    private Integer shitAmount;
    /**
     * 是否消失
     */
    private Boolean disappear;
    /**
     * 是否已经被领取过
     */
    private Boolean hasReceived;
    /**
     * 是否已经过期
     */
    private Boolean hasExpired;
    /**
     * 是否达到每日上线
     */
    private Boolean reachMaxLimit;

    public Integer getShitAmount() {
        return shitAmount;
    }

    public void setShitAmount(Integer shitAmount) {
        this.shitAmount = shitAmount;
    }

    public Boolean getDisappear() {
        return disappear;
    }

    public void setDisappear(<PERSON><PERSON><PERSON> disappear) {
        this.disappear = disappear;
    }

    public Boolean getHasReceived() {
        return hasReceived;
    }

    public void setHasReceived(Boolean hasReceived) {
        this.hasReceived = hasReceived;
    }

    public Boolean getHasExpired() {
        return hasExpired;
    }

    public void setHasExpired(Boolean hasExpired) {
        this.hasExpired = hasExpired;
    }

    public Boolean getReachMaxLimit() {
        return reachMaxLimit;
    }

    public void setReachMaxLimit(Boolean reachMaxLimit) {
        this.reachMaxLimit = reachMaxLimit;
    }
}
