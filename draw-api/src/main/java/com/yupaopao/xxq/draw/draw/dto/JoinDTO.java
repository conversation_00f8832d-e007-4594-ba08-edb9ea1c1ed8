package com.yupaopao.xxq.draw.draw.dto;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;

import java.io.Serializable;

/**
 * 加入你画我猜返回对象
 *
 * @author: liuchuan
 */
@Data
public class JoinDTO implements Serializable {

    @Description("你画我猜场次ID")
    private Long drawId;

    @Description("答案提示")
    private String answerDesc;

    @Description("答案长度")
    private Integer answerLength;

    @Description("奖励的总人数")
    private Integer awardTop;

    @Description("当前已经答对的人数")
    private Integer rightCount;

    @Description("准备倒计时秒数")
    private Integer readyDuration;

    @Description("游戏剩余倒计时秒数")
    private Integer gameDuration;

    @Description("准备倒计时秒数")
    private Integer channelSwitch;

    @Description("赞助类型")
    private Integer sponsorType;

    @Description("赞助人UID")
    private String sponsorUid;

    @Description("赞助人昵称")
    private String sponsorName;

    @Description("赞助人是否神秘人")
    private Boolean  sponsorSecret;

}
