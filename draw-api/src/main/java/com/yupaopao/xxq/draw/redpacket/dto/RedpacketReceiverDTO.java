package com.yupaopao.xxq.draw.redpacket.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019/12/21 4:27 下午
 */
@Data
public class RedpacketReceiverDTO implements Serializable {

    private String avatar;

    private String name;

    private Long redpacketAmount;

    /**
     * 神秘人标识
     */
    private Boolean secret;

    /**
     * 昵称颜色
     */
    private String nameColor;

    /**
     * 领取人uid
     */
    private Long uid;

    public RedpacketReceiverDTO(String avatar, String name, Long redpacketAmount, Boolean secret, String nameColor) {
        this.avatar = avatar;
        this.name = name;
        this.redpacketAmount = redpacketAmount;
        this.secret = secret;
        this.nameColor = nameColor;
    }

    public RedpacketReceiverDTO() {
    }
}
