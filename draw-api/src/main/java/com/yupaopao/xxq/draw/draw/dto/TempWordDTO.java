package com.yupaopao.xxq.draw.draw.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020-11-30 19:46
 */
@Setter
@Getter
public class TempWordDTO implements Serializable {
    private static final long serialVersionUID = 4354636623181754583L;
    /**
     * 记录id
     */
    private Long id;

    /**
     * 答案
     */
    private String answer;

    /**
     * 描述
     */
    private String description;

    /**
     * 直播间：0不适用，1适用
     */
    private Integer xxqType;

    /**
     * 聊天室：0不适用，1适用
     */
    private Integer yuerType;

    /**
     * 状态，0删除，1正常，2异常
     */
    private Integer status;

    /**
     * 异常原因
     */
    private String reason;
}
