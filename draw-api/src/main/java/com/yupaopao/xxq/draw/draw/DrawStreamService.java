package com.yupaopao.xxq.draw.draw;

import com.yupaopao.platform.common.annotation.executor.CommonExecutor;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.xxq.CatPoint;
import com.yupaopao.xxq.draw.draw.dto.DrawStreamDTO;
import com.yupaopao.xxq.draw.draw.request.UploadStreamRequest;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * "你画我猜"画画流服务
 *
 * @author: liuchuan
 */
public interface DrawStreamService {

    /**
     * 主播上报每一笔的画笔信息
     */
    @CatPoint(event = "Draw.uploadStream")
    @CommonExecutor(desc = "主播上报每一笔的画笔信息", printParam = true)
    Response<Void> uploadDrawStream(@NotNull UploadStreamRequest request);


    /**
     * 获取画画流信息
     *
     * @param drawId 画画ID
     * @return
     */
    @CommonExecutor(desc = "获取画画流信息", printParam = true)
    Response<List<DrawStreamDTO>> getDrawStream(@NotNull Long drawId);


    /**
     * 清空画笔
     *
     * @param drawId 画画ID
     * @return
     */
    @CommonExecutor(desc = "清空画笔", printParam = true)
    Response<Void> cleanDrawStream(@NotNull Long drawId);


}
