package com.yupaopao.xxq.draw.draw.request;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: liuchuan
 */
@Data
public class DrawGuideRequest implements Serializable {

    @NotNull
    @Description("直播间ID")
    private Long liveRoomId;


    @NotNull
    @Description("直播间ID")
    private Long uid;


}
