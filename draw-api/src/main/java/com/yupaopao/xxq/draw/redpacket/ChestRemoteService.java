package com.yupaopao.xxq.draw.redpacket;

import com.yupaopao.platform.common.annotation.executor.CommonExecutor;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.xxq.draw.redpacket.dto.request.ChestCreateRequest;
import com.yupaopao.xxq.draw.redpacket.dto.vo.ChestVO;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

public interface ChestRemoteService {

    /**
     * 发放宝箱
     *
     * @param request request
     * @return
     */
    @CommonExecutor(desc = "发放宝箱", printParam = true)
    Response<ChestVO> distribute(@NotNull @Valid ChestCreateRequest request);

    /**
     * 增加宝箱领取人数&并检查是否达到领取上限
     *
     * @param liveRoomId
     * @param chestId
     * @return
     */
    @CommonExecutor(desc = "增加宝箱领取人数&并检查是否达到领取上限", printParam = true)
    Response<Boolean> checkReceiveAmount(Long liveRoomId, Long chestId);

    /**
     * 查询宝箱
     *
     * @param liveRoomId 直播间id
     * @param chestType  宝箱类型 {@link com.yupaopao.xxq.draw.redpacket.constant.ChestTypeEnum}
     * @return
     */
    @CommonExecutor(desc = "查询宝箱", printParam = true)
    @Deprecated
    Response<Long> getByLiveRoomId(@NotNull Long liveRoomId, @NotNull String chestType);

    /**
     * 发放宝箱
     *
     * @param liveRoomId 直播间id
     * @param lotteryId  宝箱对应奖池id
     * @return
     */
    @CommonExecutor(desc = "发放宝箱", printParam = true)
    Response<Boolean> create(@NotNull Long liveRoomId, @NotNull Integer lotteryId);

    /**
     * 查询宝箱
     *
     * @param liveRoomId 直播间id
     * @return
     */
    @CommonExecutor(desc = "查询宝箱", printParam = true)
    Response<ChestVO> getByLiveRoomId(@NotNull Long liveRoomId);

    /**
     * 查询用户是否领取过宝箱
     *
     * @param liveRoomId 直播间id
     * @param chestType  宝箱类型 {@link com.yupaopao.xxq.draw.redpacket.constant.ChestTypeEnum}
     * @return
     */
    @CommonExecutor(desc = "查询宝箱", printParam = true)
    @Deprecated
    Response<Boolean> getByUid(@NotNull Long liveRoomId, @NotNull String chestType, @NotNull Long uid);

    /**
     * 领取宝箱
     *
     * @param liveRoomId 直播间id
     * @param chestType  宝箱类型 {@link com.yupaopao.xxq.draw.redpacket.constant.ChestTypeEnum}
     * @return
     */
    @CommonExecutor(desc = "领取宝箱", printParam = true)
    @Deprecated
    Response<Boolean> receive(@NotNull Long liveRoomId, @NotNull String chestType, @NotNull Long uid);

    /**
     * 查询用户是否领取过宝箱
     *
     * @param liveRoomId 直播间id
     * @return
     */
    @CommonExecutor(desc = "查询宝箱", printParam = true)
    Response<Boolean> getByUid(@NotNull Long liveRoomId, @NotNull Long uid);

    /**
     * 领取宝箱
     *
     * @param liveRoomId 直播间id
     * @return
     */
    @CommonExecutor(desc = "领取宝箱", printParam = true)
    Response<Boolean> receive(@NotNull Long liveRoomId, @NotNull Long uid);
}
