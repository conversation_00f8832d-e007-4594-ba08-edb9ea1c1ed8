package com.yupaopao.xxq.draw.draw.request;

import com.yupaopao.platform.common.annotation.Description;
import com.yupaopao.platform.common.po.MobileContext;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ToString
@Data
public class SponsorAndStartDrawRequest implements Serializable {

    @Description("用户UID")
    private Long uid;

    @Description("通道开关")
    private Integer channelSwitch = 1;

    @Description("用户accessToken")
    private String accessToken;

    @Deprecated
    @Description("直播ID")
    private Long liveId;

    @Deprecated
    @Description("用户ID")
    private String userId;

    @Description("上下文")
    private MobileContext mobileContext;
}
