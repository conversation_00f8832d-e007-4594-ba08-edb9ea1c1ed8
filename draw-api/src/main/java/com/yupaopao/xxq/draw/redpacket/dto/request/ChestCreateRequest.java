package com.yupaopao.xxq.draw.redpacket.dto.request;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
public class ChestCreateRequest implements Serializable {

    private static final long serialVersionUID = 5462078770559429209L;

    /**
     * 直播间id
     */
    @NotNull
    private Long liveRoomId;

    /**
     * 奖池id
     */
    @NotNull
    private Integer lotteryId;

    /**
     * 活动id
     */
    @NotNull
    private String actViewId;

    /**
     * 倒计时(秒)
     */
    private Long countDown;

    /**
     * 宝箱可领取有效时间(秒)
     */
    private Long effectiveTime;

    /**
     * 发放时间
     */
    private Date distributeTime;

    /**
     * 领取份数 0=不限制
     */
    private Long amount;

    /**
     * 状态
     */
    private Integer state;

}
