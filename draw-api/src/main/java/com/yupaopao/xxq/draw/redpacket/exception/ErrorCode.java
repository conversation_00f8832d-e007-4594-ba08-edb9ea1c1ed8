package com.yupaopao.xxq.draw.redpacket.exception;

import com.yupaopao.platform.common.dto.Code;

/**
 * <AUTHOR>
 * @date 2019/12/18 4:58 下午
 */
public enum ErrorCode implements Code {
    /**
     * 红包余额不足
     */
    NOT_SUFFICIENT_FUNDS("8500", "余额不足，请充值"),
    /**
     * 审核未通过
     */
    AUDIT_NOT_PASS("90001", "审核未通过"),
    /**
     * 直播间不存在
     */
    ROOM_NOT_FOUND("8001", "直播间不存在"),
    /**
     * 直播间不存在
     */
    REDPACKET_NOT_FOUND("8001", "红包不存在"),
    /**
     * 今日红包领取次数已用完
     */
    NO_ACCESS("8001", "今日红包领取次数已用完"),

    /**
     * 被踢出直播间
     */
    LIVE_KICK("8001", "您已被踢出直播间，不能领取红包"),
    /**
     * 当前红包已过期
     */
    OVERDUE("8001", "当前红包已过期"),
    /**
     * 未开始领取
     */
    NOT_START("8001", "未开始领取"),
    /**
     * 禁言
     */
    MUTE("8001", "您已被禁言"),
    /**
     * 红包数量超过上限
     */
    REDPACKET_COUNT_LIMIT("8001", "红包数量超过上限"),
    /**
     * 发红包金额超上限
     */
    REDPACKET_AMOUNT_LIMIT("8001", "发红包金额已超出今日上限，请调整"),
    /**
     * 支付失败
     */
    PAY_FAIL("8001", "支付失败"),

    /**
     * 无可玩游戏
     */
    NO_GAME("8001", "无可玩游戏"),

    /**
     * 请勿重复领取
     */
    REPEAT_RECEIVE_ERROR("8001", "您的操作过快，请稍候重试"),
    /**
     * 官方直播间暂不支持发红包
     */
    OFFICIAL_ROOM_LIMIT("8001", "官方直播间暂不支持发红包"),

    /**
     * 当前麦上没有主播，无法发红包
     */
    NOT_EXIST_HOST("8001", "当前麦上没有主播，无法发红包"),


    RED_ENVELOPE_ILLEGAL("8101", "该封面已过期，请重新选择"),


    ACCESS_TOKEN_ERROR("8001","用户信息错误"),

    REPEAT_FINISHED("8001", "红包已被领完"),
    ;

    private String code;

    private String message;

    ErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }
}
