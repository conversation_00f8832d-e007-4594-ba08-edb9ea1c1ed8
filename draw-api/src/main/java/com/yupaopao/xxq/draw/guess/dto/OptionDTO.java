package com.yupaopao.xxq.draw.guess.dto;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/6/9 10:22 上午
 */
@Data
public class OptionDTO implements Serializable {
    private static final long serialVersionUID = -3841773642468859091L;

    @Description("描述")
    private String desc;

    @Description("倍数")
    private BigDecimal multiple;

    @Description("押注总数")
    private Long amount;

    @Description("是否成功")
    private Boolean success;

    public OptionDTO(String desc, BigDecimal multiple, Long amount) {
        this.desc = desc;
        this.multiple = multiple;
        this.amount = amount;
    }
}
