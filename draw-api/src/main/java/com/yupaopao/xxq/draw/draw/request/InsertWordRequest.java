package com.yupaopao.xxq.draw.draw.request;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: liuchuan
 */
@Data
public class InsertWordRequest implements Serializable {

    @NotNull
    @Description("答案")
    private String answer;

    @NotNull
    @Description("答案描述")
    private String description;

    @NotNull
    @Description("适用范围")
    private String type;

}
