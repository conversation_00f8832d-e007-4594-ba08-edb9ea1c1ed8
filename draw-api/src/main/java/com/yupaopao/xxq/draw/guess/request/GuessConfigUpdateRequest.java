package com.yupaopao.xxq.draw.guess.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/6/10 4:07 下午
 */
@Data
public class GuessConfigUpdateRequest implements Serializable {
    private static final long serialVersionUID = 1191057226867723698L;

    @NotNull
    private Long uid;
    @NotNull
    private Long guessId;
    @NotNull
    private Boolean delete;

    private String title;

    private String leftOption;

    private String rightOption;

    private Integer deadlineMinute;
}
