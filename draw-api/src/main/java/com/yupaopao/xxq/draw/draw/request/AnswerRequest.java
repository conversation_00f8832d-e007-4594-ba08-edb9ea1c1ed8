package com.yupaopao.xxq.draw.draw.request;

import com.yupaopao.platform.common.annotation.Description;
import com.yupaopao.platform.common.po.MobileContext;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: liuchuan
 */
@Data
@Deprecated
public class AnswerRequest extends UserRequest implements Serializable {

    @NotNull
    @Description("直播场次ID")
    private Long liveId;

    @NotNull
    @Description("你画我猜ID")
    private Long drawId;

    @NotNull
    @Description("答案")
    private String answer;

    @Description("是否开启贵族弹幕")
    private Boolean barrage = false;

    @Description("上下文")
    private MobileContext mobileContext;

}
