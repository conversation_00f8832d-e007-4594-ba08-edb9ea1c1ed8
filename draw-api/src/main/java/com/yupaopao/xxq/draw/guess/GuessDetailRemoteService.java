package com.yupaopao.xxq.draw.guess;

import com.yupaopao.platform.common.annotation.executor.CommonExecutor;
import com.yupaopao.platform.common.dto.PageResult;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.xxq.draw.guess.dto.GuessDetailDTO;
import com.yupaopao.xxq.draw.guess.dto.GuessInfoDTO;
import com.yupaopao.xxq.draw.guess.request.GuessPageRequest;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 竞猜明细
 *
 * <AUTHOR>
 */
public interface GuessDetailRemoteService {

    @CommonExecutor(desc = "分页查询明细", printParam = true)
    Response<PageResult<GuessDetailDTO>> findPage(@NotNull GuessPageRequest req);

}
