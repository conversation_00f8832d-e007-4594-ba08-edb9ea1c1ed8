package com.yupaopao.xxq.draw.draw.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class WordListResponse implements Serializable {

    private Long wordId;

    private Date createTime;

    private Date updateTime;

    private Integer appId;

    private String answer;

    private String description;

    /**
     * 是否展示xxq
     */
    private Integer xxqType;

    /**
     * 是否展示yuer
     */
    private Integer yuerType;

    private Integer wordNum;

    private Integer auditStatus;

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
        this.wordNum = answer.length();
    }
}
