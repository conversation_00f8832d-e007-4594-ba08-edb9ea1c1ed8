package com.yupaopao.xxq.draw.guess.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/6/9 2:25 下午
 */
@Data
public class GuessStatisticsDTO implements Serializable {

    private static final long serialVersionUID = 4634271314158459707L;


    private Integer joinCount;

    private Integer successCount;

    private Long gain;

    public GuessStatisticsDTO(Integer joinCount, Integer successCount, Long gain) {
        this.joinCount = joinCount;
        this.successCount = successCount;
        this.gain = gain;
    }

    public GuessStatisticsDTO() {
    }
}
