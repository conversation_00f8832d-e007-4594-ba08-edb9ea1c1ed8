package com.yupaopao.xxq.draw.guess.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
public class GuessBanPageRequest implements Serializable {

    private static final long serialVersionUID = -1345256968237122421L;
    @NotNull
    private Integer offset;

    @NotNull
    private Integer limit;

    private Date startTme;

    private Date endTime;

    private Long uid;
}
