package com.yupaopao.xxq.draw.draw.dto;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;

import java.io.Serializable;

/**
 * 主播规则说明返回实体
 *
 * @author: liuchuan
 */
@Data
public class AnchorRuleDTO implements Serializable {

    @Description("赞助次数")
    private Integer chance;

    @Description("规则描述")
    private String ruleDesc;

    @Description("规则跳转页面")
    private String ruleUrl;

    @Description("主播付费开始游戏价格")
    private Integer sponsorDiamond;

}
