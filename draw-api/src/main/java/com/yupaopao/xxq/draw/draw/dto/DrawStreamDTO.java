package com.yupaopao.xxq.draw.draw.dto;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: liuchuan
 */
@Data
public class DrawStreamDTO implements Serializable {

    @Description("组")
    private Integer group;

    @Description("序号ID")
    private Integer index;

    @Description("流内容")
    private String stream;

}
