package com.yupaopao.xxq.draw.draw.request;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: liuchuan
 */
@Data
public class DrawAnswerRequest implements Serializable {

    @NotNull
    @Description("直播间ID")
    private Long liveRoomId;

    @NotNull
    @Description("你画我猜场次ID")
    private Long drawId;

    @NotNull
    @Description("答案")
    private String answer;

    @NotNull
    @Description("用户ID")
    private Long uid;

    @NotNull
    @Description("业务线ID")
    private Integer appId;

    @Description("是否开启贵族弹幕")
    private Boolean barrage = false;

    private String deviceId;

    private String clientIp;


}
