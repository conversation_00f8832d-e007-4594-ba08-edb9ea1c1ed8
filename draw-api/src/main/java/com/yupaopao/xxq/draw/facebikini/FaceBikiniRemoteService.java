package com.yupaopao.xxq.draw.facebikini;

import com.yupaopao.platform.common.annotation.executor.CommonExecutor;
import com.yupaopao.platform.common.dto.PageResult;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.platform.common.po.MobileContext;
import com.yupaopao.xxq.CatPoint;
import com.yupaopao.xxq.draw.facebikini.dto.FaceBikiniInfoDTO;
import com.yupaopao.xxq.draw.facebikini.request.PlayRequest;
import com.yupaopao.xxq.draw.facebikini.request.ReportRequest;

/**
 * <AUTHOR>
 * @date 2020/5/19 3:03 下午
 */
public interface FaceBikiniRemoteService {


    /**
     * 更新报名费
     * @param uid
     * @param fee
     * @return
     */
    @CommonExecutor(desc = "更新报名费", printParam = true, printResponse = true)
    Response<Boolean>  updateFee(Long uid, Long fee);

    /**
     * 获取信息
     * @param liveRoomId
     * @return
     */
    @CommonExecutor(desc = "获取信息")
    Response<FaceBikiniInfoDTO> info(Long liveRoomId, Long uid);

    /**
     * 加入排队
     * @param uid
     * @param liveRoomId
     * @return
     */
    @CatPoint(event = "FaceKini.join")
    @CommonExecutor(desc = "排队", printParam = true)
    Response<Integer> join(Long uid, Long liveRoomId, String accessToken, String riskTraceId, MobileContext mobileContext);

    /**
     * 排队列表
     * @param liveRoomId
     * @param id
     * @param limit
     * @return
     */
    @CommonExecutor(desc = "排队列表")
    Response<PageResult<Long>> queueList(Long liveRoomId, Long id, Integer limit);

    /**
     * 主播开始游戏
     * @param uid
     * @return
     */
    @CatPoint(event = "FaceKini.start")
    @CommonExecutor(desc = "主播开始游戏", printParam = true)
    Response<Boolean> start(Long uid);

    /**
     * 使用道具
     * @param request
     * @return
     */
    @CatPoint(event = "FaceKini.play")
    @CommonExecutor(desc = "使用道具", printParam = true)
    Response<Boolean> play(PlayRequest request);

    /**
     * 上报结果
     * @param reportRequest
     * @return
     */
    @CatPoint(event = "FaceKini.report")
    @CommonExecutor(desc = "上报结果", printParam = true)
    Response<Boolean> report(ReportRequest reportRequest);

    /**
     * 取消报名
     * @param liveRoomId
     * @param uid
     * @return
     */
    @CommonExecutor(desc = "取消", printParam = true)
    Response<Boolean> cancel(Long liveRoomId, Long uid);
}
