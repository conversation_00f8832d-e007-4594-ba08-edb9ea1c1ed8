package com.yupaopao.xxq.draw.redpacket;

import com.yupaopao.platform.common.annotation.executor.CommonExecutor;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.xxq.draw.redpacket.dto.request.RedpacketCoverRequest;
import com.yupaopao.xxq.draw.redpacket.dto.vo.RedpacketCoverQueryVO;

public interface RedpacketCoverRemoteService {
    @CommonExecutor(printParam = true)
    Response<RedpacketCoverQueryVO> queryCover(RedpacketCoverRequest request);
}
