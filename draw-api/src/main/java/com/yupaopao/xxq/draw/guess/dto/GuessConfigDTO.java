package com.yupaopao.xxq.draw.guess.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/6/9 5:28 下午
 */
@Data
public class GuessConfigDTO implements Serializable {
    private static final long serialVersionUID = -2480967307393733812L;

    private Long id;
    /**
     * 房间id
     */
    private Long liveRoomId;

    /**
     * 主播id
     */
    private Long uid;

    /**
     * 竞猜标题
     */
    private String title;

    /**
     * 左选项
     */
    private String leftOption;

    /**
     * 右选项
     */
    private String rightOption;

    /**
     * 竞猜截止分钟数
     */
    private Integer deadlineMinute;

    /**
     * 风控状态 0审核中 1审核通过 2审核拒绝
     */
    private Integer riskState;

    /**
     * 风控拒绝原因
     */
    private String refuseReason;

    /**
     * 是否删除
     */
    private Boolean isDelete;
}
