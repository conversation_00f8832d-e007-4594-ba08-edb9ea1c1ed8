package com.yupaopao.xxq.draw.draw.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
public class LiveLeaveMessageDTO implements Serializable {

    private String anchorId;

    private Long anchorUid;

    private Integer appId;

    private String chatroomId;

    private String coverImg;

    private Long liveId;

    private Long liveRoomId;

    private Integer liveType;

    private Date timestamp;

    private String title;

    private Long uid;

    private String userId;

    private String leaveType;

}
