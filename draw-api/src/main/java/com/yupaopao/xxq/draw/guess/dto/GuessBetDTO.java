package com.yupaopao.xxq.draw.guess.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/6/9 11:48 上午
 */
@Data
public class GuessBetDTO implements Serializable {
    private static final long serialVersionUID = 7046325226299285473L;

    private Date createTime;

    /**
     * 竞猜id
     */
    private Long guessId;

    /**
     * 0左选项 1右选项
     */
    private Integer betOption;

    /**
     * 押注量
     */
    private Long betAmount;

    /**
     * 结算盈利
     */
    private BigDecimal settleAmount;

    /**
     * 用户id
     */
    private Long uid;

    /**
     * 1 成功 0 失败
     */
    private Integer success;

    /**
     * 0投注中1停止投注2已结算3流局
     */
    private Integer state;
}
