package com.yupaopao.xxq.draw.guess.request;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class GuessPageRequest implements Serializable {

    private static final long serialVersionUID = -7403591162642030458L;
    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 竞猜idList
     */
    private List<Long> guessIds;

    /**
     * 用户uid
     */
    private Long uid;

    /**
     * page
     */
    private Integer page;

    /**
     * size
     */
    private Integer size;
}
