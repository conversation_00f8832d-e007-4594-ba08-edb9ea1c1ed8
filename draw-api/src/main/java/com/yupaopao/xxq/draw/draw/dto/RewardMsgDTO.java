package com.yupaopao.xxq.draw.draw.dto;

import java.io.Serializable;
import java.util.List;

public class RewardMsgDTO implements Serializable {
    private String title;
    private String content;
    private String desc;
    private String jumpDesc;
    private String shopUrl;
    private List<SimpleRewardDTO> rewards;

    private String btnDesc; //前往星星乐园
    private String jumpEdenUrl; // 跳转星星乐园url
    private Boolean received;// 是否全部领取
    private String bottomContent; //  今日星星未全部领完时，展示文案”剩余待领取星星：X“; 今日星星已全部领完后，展示文案”奥利给！今日星星已全部领完“
    private String jumpTaskString; // "去领取"
    private String jumpTaskUrl; //  跳转任务面板url
    private String bubbleString; //  ”可兑换装扮“ 在星星转盘“或”星星商城“中，有过一次星星消耗记录后，不再展示气泡提示
    private Integer gamePlay;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getShopUrl() {
        return shopUrl;
    }

    public void setShopUrl(String shopUrl) {
        this.shopUrl = shopUrl;
    }

    public List<SimpleRewardDTO> getRewards() {
        return rewards;
    }

    public void setRewards(List<SimpleRewardDTO> rewards) {
        this.rewards = rewards;
    }

    public String getJumpDesc() {
        return jumpDesc;
    }

    public void setJumpDesc(String jumpDesc) {
        this.jumpDesc = jumpDesc;
    }

    public String getBtnDesc() {
        return btnDesc;
    }

    public void setBtnDesc(String btnDesc) {
        this.btnDesc = btnDesc;
    }

    public String getJumpEdenUrl() {
        return jumpEdenUrl;
    }

    public void setJumpEdenUrl(String jumpEdenUrl) {
        this.jumpEdenUrl = jumpEdenUrl;
    }

    public Boolean getReceived() {
        return received;
    }

    public void setReceived(Boolean received) {
        this.received = received;
    }

    public String getBottomContent() {
        return bottomContent;
    }

    public void setBottomContent(String bottomContent) {
        this.bottomContent = bottomContent;
    }

    public String getJumpTaskString() {
        return jumpTaskString;
    }

    public void setJumpTaskString(String jumpTaskString) {
        this.jumpTaskString = jumpTaskString;
    }

    public String getJumpTaskUrl() {
        return jumpTaskUrl;
    }

    public void setJumpTaskUrl(String jumpTaskUrl) {
        this.jumpTaskUrl = jumpTaskUrl;
    }

    public String getBubbleString() {
        return bubbleString;
    }

    public void setBubbleString(String bubbleString) {
        this.bubbleString = bubbleString;
    }

    public Integer getGamePlay() {
        return gamePlay;
    }

    public void setGamePlay(Integer gamePlay) {
        this.gamePlay = gamePlay;
    }
}
