package com.yupaopao.xxq.draw.facebikini.dto;

import com.yupaopao.platform.common.annotation.Description;
import com.yupaopao.xxq.draw.draw.dto.FreeGameCardDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/25 3:10 下午
 */
@Data
public class FaceBikiniInfoDTO implements Serializable {
    private static final long serialVersionUID = -7676356511545832946L;

    private Long fee;

    private List<Long> queueList;

    private Integer queueSize;

    private Long anchorUid;

    private Long liveRoomId;

    /**
     * 主播正在进行的游戏
     */
    private String playingGame;

    private Boolean hasJoin;

    private Integer queueIndex;

    @Description("免费游戏卡")
    private FreeGameCardDTO freeGameCard;
}
