package com.yupaopao.xxq.draw.draw.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: liuchuan
 */
@Data
public class ChanceDTO implements Serializable {

    /**
     * 机会ID
     */
    private Long chanceId;

    /**
     * 赞助人ID
     */
    private String sponsorId;


    /**
     * 赞助人ID
     */
    private Long sponsorUId;


    private Date createTime;

    private Integer status;


    private String avatar;


    private String username;


    private Integer gender;

    private String levelIcon;


    private Boolean secret;

}
