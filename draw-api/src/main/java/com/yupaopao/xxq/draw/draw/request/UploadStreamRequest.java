package com.yupaopao.xxq.draw.draw.request;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: l<PERSON><PERSON><PERSON>
 */
@ToString
@Data
public class UploadStreamRequest implements Serializable {

    @NotNull
    @Description("直播ID")
    private Long liveId;

    @NotNull
    @Description("画ID")
    private Long drawId;

    @NotNull
    @Description("组")
    private Integer group;

    @NotNull
    @Description("序号ID")
    private Integer index;

    @NotNull
    @Description("流内容")
    private String stream;

}
