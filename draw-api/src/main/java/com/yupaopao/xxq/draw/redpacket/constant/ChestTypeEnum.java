package com.yupaopao.xxq.draw.redpacket.constant;

import org.springframework.util.StringUtils;

public enum ChestTypeEnum {

    HOUR_CHEST("HOUR_CHEST", "小时榜冠军宝箱"),

    ;

    private String type;

    private String desc;

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    ChestTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static ChestTypeEnum getTypeEnum(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        for (ChestTypeEnum typeEnum : values()) {
            if (typeEnum.type.equalsIgnoreCase(type)) {
                return typeEnum;
            }
        }
        return null;
    }
}
