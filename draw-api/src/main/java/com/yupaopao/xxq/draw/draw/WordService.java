package com.yupaopao.xxq.draw.draw;

import com.yupaopao.platform.common.annotation.executor.CommonExecutor;
import com.yupaopao.platform.common.dto.PageResult;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.xxq.draw.draw.dto.TempWordListDTO;
import com.yupaopao.xxq.draw.draw.dto.WordListResponse;
import com.yupaopao.xxq.draw.draw.request.InsertWordRequest;
import com.yupaopao.xxq.draw.draw.request.LimitRequest;
import com.yupaopao.xxq.draw.draw.request.RandomWordListRequest;
import com.yupaopao.xxq.draw.draw.request.TempWordListRequest;
import com.yupaopao.xxq.draw.draw.request.UpdateTempWordRequest;
import com.yupaopao.xxq.draw.draw.request.UpdateWordRequest;
import com.yupaopao.xxq.draw.draw.request.WordListRequest;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * "你画我猜"词库服务
 *
 * @author: liuchuan
 */
public interface WordService {

    /**
     * 添加词库
     */
    @CommonExecutor(desc = "添加词库", printParam = true)
    Response<Void> insert(@NotNull InsertWordRequest request);

    /**
     * 添加词库
     */
    @CommonExecutor(desc = "添加词库", printParam = true)
    Response<Void> insert(@NotNull List<InsertWordRequest> request);

    /**
     * 删除
     */
    @CommonExecutor(desc = "删除", printParam = true)
    Response<Void> deleteWord(@NotNull Long wordId);

    /**
     * 修改
     */
    @CommonExecutor(desc = "修改", printParam = true)
    Response<Void> updateWord(@NotNull UpdateWordRequest request);

    /**
     * 列表
     */
    @CommonExecutor(desc = "列表", printParam = true)
    Response<PageResult<WordListResponse>> wordList(@NotNull @Valid WordListRequest request);

    /**
     * 临时列表查询
     */
    @CommonExecutor(desc = "临时列表查询", printParam = true)
    Response<TempWordListDTO> tempWordList(@NotNull @Valid LimitRequest request);

    /**
     * 临时列表添加
     */
    @CommonExecutor(desc = "临时列表添加", printParam = true)
    Response<Boolean> addTempWordList(@NotNull @Valid List<TempWordListRequest> requestList);

    /**
     * 临时表数据删除
     */
    @CommonExecutor(desc = "删除临时表数据", printParam = true)
    Response<Boolean> deleteTempWord(@NotNull @Valid Long id);

    /**
     * 临时表数据更新
     */
    @CommonExecutor(desc = "更新临时表列表", printParam = true)
    Response<Boolean> updateTempWord(@NotNull @Valid UpdateTempWordRequest request);

    /**
     * 确认导入
     */
    @CommonExecutor(desc = "列表确认导入", printParam = true)
    Response<Boolean> confirmImport(@NotNull @Valid String uploadId);

    /**
     * 随机词组
     */
    @CommonExecutor(desc = "聊天室-随机词组", printParam = true)
    Response<List<WordListResponse>> randomWordList(@NotNull @Valid RandomWordListRequest request);
}
