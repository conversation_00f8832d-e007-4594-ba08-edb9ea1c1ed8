package com.yupaopao.xxq.draw.guess.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class GuessBanDTO implements Serializable {

    private static final long serialVersionUID = -2279037114408350598L;
    /**
     * 主播uid
     */
    private Long uid;

    /**
     * id
     */
    private Long id;

    /**
     * 时效
     *
     * @see com.yupaopao.xxq.draw.guess.constant.GuessTermEnum
     */
    private Integer term;

    /**
     * 封禁开始时间
     */
    private Date startTime;

    /**
     * 封禁结束时间
     */
    private Date endTime;

    /**
     * 封禁原因
     */
    private String reason;

    /**
     * 证据列表
     */
    private List<String> imgList;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
