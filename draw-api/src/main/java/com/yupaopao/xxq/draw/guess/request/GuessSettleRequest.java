package com.yupaopao.xxq.draw.guess.request;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/9 5:25 下午
 */
@Data
public class GuessSettleRequest implements Serializable {
    private static final long serialVersionUID = 8982518889039323951L;

    private Long uid;

    @NotNull
    private List<SettleResult> settleResultList;

    @Data
    public static class SettleResult implements Serializable{

        private static final long serialVersionUID = 7084272860225246757L;

        @Description("竞猜id")
        @NotNull
        private Long guessId;

        @Description("是否成功")
        @NotNull
        private Boolean success;

        @Description("正确答案 0左边 1右边")
        private Integer option;
    }
}
