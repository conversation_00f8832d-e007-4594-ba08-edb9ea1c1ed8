package com.yupaopao.xxq.draw.draw.dto;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;

import java.io.Serializable;

/**
 * 引导弹窗
 *
 * @author: liuchuan
 */
@Data
public class DrawGuideDTO implements Serializable {

    @Description("画猜卡ID")
    private Long goodsId;

    @Description("画猜卡类型")
    private Integer goodsType;

    @Description("引导文案")
    private String guideContent;

    @Description("弹窗时长")
    private Integer duration;

    @Description("引导按钮文案")
    private String guideButton;

}
