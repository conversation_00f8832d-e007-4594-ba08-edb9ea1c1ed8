package com.yupaopao.xxq.draw.guess.constant;

/**
 * <AUTHOR>
 * @date 2020/6/6 3:11 下午
 */
public enum GuessIssueStateEnum {

    /**
     * 投注中
     */
    BETTING(0),
    /**
     * 停止投注
     */
    STOP_BET(1),
    /**
     * 已结算
     */
    SETTLED(2),
    /**
     * 流局
     */
    INVAILD(3);


    GuessIssueStateEnum(int value) {
        this.value = value;
    }

    private int value;

    public int getValue() {
        return value;
    }

    public static GuessIssueStateEnum getByName(String name) {
        for (GuessIssueStateEnum value : values()) {
            if (value.name().equalsIgnoreCase(name)) {
                return value;
            }
        }
        return null;
    }

    public static GuessIssueStateEnum getByValue(int value) {
        for (GuessIssueStateEnum state : values()) {
            if (state.getValue() == value) {
                return state;
            }
        }
        return null;
    }
}
