package com.yupaopao.xxq.draw.draw.dto;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户规则说明返回实体
 *
 * @author: liuchuan
 */
@Data
public class UserRuleDTO implements Serializable {

    @Description("赞助消费钻石")
    private Integer sponsorDiamond;

    @Description("规则描述")
    private String ruleDesc;

    @Description("规则跳转页面")
    private String ruleUrl;

    @Description("免费游戏卡")
    private FreeGameCardDTO  freeGameCard;

}
