package com.yupaopao.xxq.draw.redpacket.dto.vo;

import com.yupaopao.xxq.draw.redpacket.dto.RedpacketCoverDTO;
import java.io.Serializable;
import java.util.List;

public class RedpacketCoverQueryVO implements Serializable {
    /**
     * 主播昵称
     */
    private String anchorUsername;
    /**
     * 主播粉丝团名称
     */
    private String fansClubName;
    /**
     * 主播粉丝团最高等级
     */
    private int maxFansClubRank;
    /**
     * 红包封面列表
     */
    private List<RedpacketCoverDTO> covers;
    /**
     * 默认红包封面
     */
    private RedpacketCoverDTO aDefault;

    /**
     * 红包最小个数
     */
    public Integer minCount;

    public String getAnchorUsername() {
        return anchorUsername;
    }

    public void setAnchorUsername(String anchorUsername) {
        this.anchorUsername = anchorUsername;
    }

    public String getFansClubName() {
        return fansClubName;
    }

    public void setFansClubName(String fansClubName) {
        this.fansClubName = fansClubName;
    }

    public List<RedpacketCoverDTO> getCovers() {
        return covers;
    }

    public void setCovers(List<RedpacketCoverDTO> covers) {
        this.covers = covers;
    }

    public RedpacketCoverDTO getaDefault() {
        return aDefault;
    }

    public void setaDefault(RedpacketCoverDTO aDefault) {
        this.aDefault = aDefault;
    }

    public int getMaxFansClubRank() {
        return maxFansClubRank;
    }

    public void setMaxFansClubRank(int maxFansClubRank) {
        this.maxFansClubRank = maxFansClubRank;
    }

    public Integer getMinCount() {
        return minCount;
    }

    public void setMinCount(Integer minCount) {
        this.minCount = minCount;
    }
}
