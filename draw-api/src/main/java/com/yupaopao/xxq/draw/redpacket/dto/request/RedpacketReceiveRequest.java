package com.yupaopao.xxq.draw.redpacket.dto.request;

import com.yupaopao.platform.common.annotation.Description;
import com.yupaopao.platform.common.po.MobileContext;
import com.yupaopao.xxq.draw.redpacket.dto.base.ClientInfo;
import lombok.Data;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2019/12/21 10:35 上午
 */
@Data
public class RedpacketReceiveRequest implements Serializable {

    private Long uid;

    private String userId;

    private String redpacketNum;

    private Long liveRoomId;

    private ClientInfo clientInfo;

    @Description("上下文")
    private MobileContext mobileContext;
}
