package com.yupaopao.xxq.draw.guess.dto;

import com.yupaopao.xxq.draw.guess.constant.GuessIssueStateEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class GuessDetailDTO implements Serializable {

    private static final long serialVersionUID = -5740619411270779464L;
    /**
     * 明细id
     */
    private Long id;

    /**
     * 竞猜id
     */
    private Long guessId;

    /**
     * 投注选项
     */
    private Integer betOption;

    /**
     * 押注量
     */
    private Long betAmount;

    /**
     * 盈利
     */
    private Double settleAmount;

    private Long uid;

    /**
     * 竞猜结果
     */
    private Integer success;

    /**
     * 状态
     *
     * @see GuessIssueStateEnum
     */
    private Integer status;

    /**
     * 交易id
     */
    private String tradeId;
}
