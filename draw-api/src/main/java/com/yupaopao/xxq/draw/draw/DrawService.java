package com.yupaopao.xxq.draw.draw;

import com.yupaopao.platform.common.annotation.executor.CommonExecutor;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.xxq.CatPoint;
import com.yupaopao.xxq.draw.draw.dto.AnchorRuleDTO;
import com.yupaopao.xxq.draw.draw.dto.AnswerDTO;
import com.yupaopao.xxq.draw.draw.dto.ChanceDTO;
import com.yupaopao.xxq.draw.draw.dto.ChannelSwitchDTO;
import com.yupaopao.xxq.draw.draw.dto.DrawGuideDTO;
import com.yupaopao.xxq.draw.draw.dto.DrawStartDTO;
import com.yupaopao.xxq.draw.draw.dto.JoinDTO;
import com.yupaopao.xxq.draw.draw.dto.UserRuleDTO;
import com.yupaopao.xxq.draw.draw.request.AnswerRequest;
import com.yupaopao.xxq.draw.draw.request.DrawAnswerRequest;
import com.yupaopao.xxq.draw.draw.request.DrawGuideRequest;
import com.yupaopao.xxq.draw.draw.request.FreeGameCardSponsorRequest;
import com.yupaopao.xxq.draw.draw.request.JoinDrawRequest;
import com.yupaopao.xxq.draw.draw.request.SponsorAndStartDrawRequest;
import com.yupaopao.xxq.draw.draw.request.SponsorRequest;
import com.yupaopao.xxq.draw.draw.request.StartDrawRequest;
import com.yupaopao.xxq.draw.draw.request.UserRequest;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * "你画我猜"画画流服务
 *
 * @author: liuchuan
 */
public interface DrawService {

    /**
     * 主播规则页面
     */
    @CommonExecutor(desc = "主播规则页面", printParam = true)
    Response<AnchorRuleDTO> ruleForAnchor(@NotNull UserRequest request, @NotNull Long liveId);

    /**
     * 用户规则页面
     */
    @CommonExecutor(desc = "用户规则页面", printParam = true)
    Response<UserRuleDTO> ruleForUser();

    /**
     * 用户赞助
     */
    @CatPoint(event = "Draw.sponsor")
    @CommonExecutor(desc = "用户赞助", printParam = true)
    Response<Boolean> sponsor(@NotNull SponsorRequest request);

    /**
     * 用户赞助
     */
    @CatPoint(event = "Draw.sponsor")
    @CommonExecutor(desc = "画猜免费赞助卡赞助", printParam = true, printResponse = true)
    Response<Boolean> freeGameCardSponsor(FreeGameCardSponsorRequest request);

    /**
     * 用户赞助列表
     */
    @CommonExecutor(desc = "用户赞助列表", printParam = true)
    Response<List<ChanceDTO>> sponsorList(@NotNull UserRequest request, @NotNull Long liveId);

    /**
     * 开始画画
     */
    @CatPoint(event = "Draw.start")
    @CommonExecutor(desc = "开始画画", printParam = true)
    Response<DrawStartDTO> startV2(@NotNull StartDrawRequest request);

    /**
     * 主播付费-开始画画
     */
    @CatPoint(event = "Draw.sponsorAndStart")
    @CommonExecutor(desc = "主播赞助并开始画画", printParam = true)
    Response<DrawStartDTO> sponsorAndStartV2(@NotNull SponsorAndStartDrawRequest request);


    /**
     * 用户中途进入（v520新增）
     */
    @CommonExecutor(desc = "用户中途进入（v520新增）", printParam = true)
    Response<JoinDTO> join(@NotNull JoinDrawRequest request);

    /**
     * 结算
     */
    @CatPoint(event = "Draw.settle")
    @CommonExecutor(desc = "结算", printParam = true)
    Response<Boolean> settle(@NotNull UserRequest request, @NotNull Long drawId);

    /**
     * 获取长连通道配置开关
     */
    @CommonExecutor(desc = "获取长连通道配置开关", printParam = true)
    Response<ChannelSwitchDTO> getChannelSwitch(Long uid);

    /**
     * 答题（v520新增接口）
     */
    @CatPoint(event = "Draw.answer")
    @CommonExecutor(desc = "答题（v520新增接口）", printParam = true)
    Response<AnswerDTO> answerV2(@NotNull DrawAnswerRequest request);


    /**
     * 画猜引导
     */
    @CommonExecutor(desc = "画猜引导", printParam = true, printResponse = true)
    Response<DrawGuideDTO> guide(DrawGuideRequest request);


    ///////////////////////////////////以下过期方法后续删除/////////////////////////////////////////////////////

    /**
     * 开始画画（过期）
     */
    @Deprecated
    @CatPoint(event = "Draw.start")
    @CommonExecutor(desc = "开始画画", printParam = true)
    Response<Boolean> start(@NotNull StartDrawRequest request);


    /**
     * 用户中途进入：兼容v510（包含510）以下版本
     */
    @Deprecated
    @CommonExecutor(desc = "用户中途进入：兼容v510（包含510）以下版本", printParam = true)
    Response<Void> join(@NotNull Long liveId, String userId, Long uid);

    /**
     * 答题（返回答题成功或者失败）:兼容v510（包含510）以下版本
     */
    @Deprecated
    @CatPoint(event = "Draw.answer")
    @CommonExecutor(desc = "答题", printParam = true)
    Response<Boolean> answer(@NotNull DrawAnswerRequest request);


}
