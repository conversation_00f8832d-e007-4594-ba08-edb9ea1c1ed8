package com.yupaopao.xxq.draw.redpacket.dto.base;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019/12/20 6:00 下午
 */
@Data
public class ClientInfo implements Serializable {

    /**
     * 用户登录校验
     */
    private String accessToken;
    /**
     * appId
     */
    private Integer appId;

    /**
     * 设备号
     */
    private String deviceId;

    /**
     * ip
     */
    private String clientIp;

    public ClientInfo(String accessToken, Integer appId, String deviceId, String clientIp) {
        this.accessToken = accessToken;
        this.appId = appId;
        this.deviceId = deviceId;
        this.clientIp = clientIp;
    }

    public ClientInfo() {
    }
}
