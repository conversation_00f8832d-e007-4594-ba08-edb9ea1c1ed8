package com.yupaopao.xxq.draw.guess;


import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.xxq.draw.guess.dto.GuessInfoDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/9 10:21 上午
 */
public interface GuessQueryRemoteService {

    /**
     * 查询直播间发布的竞猜
     *
     * @param liveRoomId
     * @return
     */
    Response<List<GuessInfoDTO>> queryIssueGuess(Long liveRoomId);


    /**
     * 批量获取
     * @param guessIdList
     * @return
     */
    Response<List<GuessInfoDTO>> queryIssueGuessByIdList(List<Long> guessIdList);

}
