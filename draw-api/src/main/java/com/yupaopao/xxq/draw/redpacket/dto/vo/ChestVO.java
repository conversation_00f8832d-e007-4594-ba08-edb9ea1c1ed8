package com.yupaopao.xxq.draw.redpacket.dto.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ChestVO implements Serializable {
    private static final long serialVersionUID = 4381225185238253729L;

    /**
     * 宝箱开启时间(时间戳)
     */
    private Long openTime;

    /**
     * 宝箱出现时间(时间戳)
     */
    private Long appearTime;

    /**
     * 宝箱对应奖池id
     */
    private Integer lotteryId;

    /**
     * 活动id
     */
    private String actViewId;

    /**
     * 领取份数 0=不限制
     */
    private Long amount;

    /**
     * 宝箱id
     */
    private Long chestId;
}
