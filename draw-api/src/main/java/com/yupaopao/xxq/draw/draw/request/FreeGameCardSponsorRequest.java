package com.yupaopao.xxq.draw.draw.request;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class FreeGameCardSponsorRequest implements Serializable {

    @NotNull
    @Description("直播间ID")
    private Long liveId;

    @NotNull
    @Description("用户UID")
    private Long uid;

    @NotNull
    @Description("主播UID")
    private Long anchorUid;

    @NotNull
    @Description("bizId")
    private String bizId;

}
