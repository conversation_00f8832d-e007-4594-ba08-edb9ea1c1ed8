package com.yupaopao.xxq.draw.guess.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class GuessBanRequest implements Serializable {

    private static final long serialVersionUID = -1324601151476901003L;
    /**
     * 证据列表
     */
    private List<String> imgList;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 主播id
     */
    @NotNull
    private Long uid;

    /**
     * 是否永久
     */
    @NotNull
    private Boolean forever;

    /**
     * 原因
     */
    private String reason;

    /**
     * 操作人
     */
    private String operator;
}
