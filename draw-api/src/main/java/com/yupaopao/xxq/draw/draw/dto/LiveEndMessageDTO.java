package com.yupaopao.xxq.draw.draw.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: 李辉
 * @date: 2018/12/13 14:32
 */
@Getter
@Setter
public class LiveEndMessageDTO implements Serializable {

    private String anchorId;

    private Long anchorUid;

    private Integer appId;

    private Long categoryId;

    private String chatroomId;

    private String coverImg;

    private Integer duration;

    private Date endTime;

    private Long liveId;

    private Long liveRoomId;

    private Integer liveType;

    private Date startTime;

    private String status;

    private String title;

    private Date timestamp;

}
