package com.yupaopao.xxq.draw.redpacket.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/11/19 5:36 下午
 */
@Data
public class RedpacketCreateDTO implements Serializable {

    private static final long serialVersionUID = 2005071768064290236L;

    /**
     * 直播间id
     */
    private String liveRoomId;
    /**
     * 描述
     */
    private String desc;

    /**
     * 是否触发全服通告
     */
    private Boolean worldMsg = false;

    /**
     * 是否神秘人
     */
    private boolean secret = false;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称颜色
     */
    private String nameColor;

    /**
     * 主播名
     */
    private String recUsername;

    /**
     * 红包icon
     */
    private String giftIcon;

    /**
     * 红包名称
     */
    private String giftName;

    /**
     * scheme
     */
    private String schemeUrl;

    public RedpacketCreateDTO(String desc) {
        this.desc = desc;
    }
}
