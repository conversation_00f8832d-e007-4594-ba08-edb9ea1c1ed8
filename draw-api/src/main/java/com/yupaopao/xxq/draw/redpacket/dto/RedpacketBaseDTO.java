package com.yupaopao.xxq.draw.redpacket.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/12/21 4:28 下午
 */
@Data
public class RedpacketBaseDTO implements Serializable {

    private Long liveRoomId;

    private Integer status;

    private Date canReceiveTime;

    private Long uid;

    private String senderAvatar;

    private String senderName;

    private Long amount;

    private Integer count;

    private Date endTime;

    private String redpacketNum;

    private Integer conditionType;

    private String conditionValue;

    /**
     * 开始倒计时毫秒数
     */
    private Long startCountDown;

    /**
     * 红包队列
     */
    private Integer queueSize;

    /**
     * 主播UID
     */
    private Long anchorUid;

    private Long coverId;

    /**
     * 神秘人标识
     */
    private Boolean secret;

}
