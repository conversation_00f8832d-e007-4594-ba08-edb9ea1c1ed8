package com.yupaopao.xxq.draw.redpacket;

import com.yupaopao.platform.common.annotation.executor.CommonExecutor;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.xxq.CatPoint;
import com.yupaopao.xxq.draw.redpacket.dto.RedpacketBaseDTO;
import com.yupaopao.xxq.draw.redpacket.dto.RedpacketCreateDTO;
import com.yupaopao.xxq.draw.redpacket.dto.RedpacketDTO;
import com.yupaopao.xxq.draw.redpacket.dto.RoomRedpacketDTO;
import com.yupaopao.xxq.draw.redpacket.dto.request.LiveRoomListRequest;
import com.yupaopao.xxq.draw.redpacket.dto.request.RedpacketCreateRequest;
import com.yupaopao.xxq.draw.redpacket.dto.request.RedpacketReceiveRequest;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/19 3:12 下午
 */
public interface RedpacketService {

    /**
     * 校验红包参数
     *
     * @param request
     * @return
     */
    @CommonExecutor(printParam = true, printResponse = true)
    Response<Boolean> check(RedpacketCreateRequest request);

    /**
     * 创建红包
     *
     * @param request
     * @return
     */
    @CatPoint(event = "Redpacket.create")
    @CommonExecutor(printParam = true, printResponse = true)
    Response<String> create(RedpacketCreateRequest request);

    /**
     * 创建红包
     *
     * @param request
     * @return
     */
    @CatPoint(event = "Redpacket.create")
    @CommonExecutor(printParam = true, printResponse = true)
    Response<RedpacketCreateDTO> createV2(RedpacketCreateRequest request);

    /**
     * 领取红包
     *
     * @param request
     * @return
     */
    @CatPoint(event = "Redpacket.receive")
    @CommonExecutor(printParam = true)
    Response<RedpacketBaseDTO> receive(RedpacketReceiveRequest request);

    /**
     * 直播间红包信息
     *
     * @param redpacketNum
     * @param uid
     * @return
     */
    @CommonExecutor(printParam = true)
    Response<RoomRedpacketDTO> roomRedpacketInfo(String redpacketNum, Long uid);

    /**
     * 批量获取直播间关联的红包基本信息
     *
     * @param liveRoomIdList
     * @return
     */
    @CommonExecutor
    Response<List<RedpacketBaseDTO>> queryRedpacketBaseInfo(@Size(max = 20, min = 1) List<Long> liveRoomIdList);

    /**
     * 批量获取直播间红包信息
     *
     * @param liveRoomIdList
     * @return
     */
    @CommonExecutor
    Response<List<RedpacketDTO>> queryRedpacketInfo(@Size(max = 20, min = 1) List<Long> liveRoomIdList);


    /**
     * 批量获取直播间红包信息 (同queryRedpacketInfo，提供给h5接口)
     *
     * @param request
     * @return
     */
    @CommonExecutor
    Response<List<RedpacketDTO>> queryRedpacketInfoForWap(LiveRoomListRequest request);

    /**
     * 临时退款接口
     *
     * @return
     */
    @CommonExecutor
    Response<Boolean> templateRefund();

    /**
     * 临时退款接口
     * @param redpacketNum
     * @param amount
     * @return
     */
    @CommonExecutor
    Response<Boolean> templateRefund(String redpacketNum, Long amount);
}
