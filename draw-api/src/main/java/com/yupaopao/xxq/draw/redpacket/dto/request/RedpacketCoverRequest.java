package com.yupaopao.xxq.draw.redpacket.dto.request;

import java.io.Serializable;

public class RedpacketCoverRequest implements Serializable {
    private String liveRoomId;
    private String uid;

    public String getLiveRoomId() {
        return liveRoomId;
    }

    public void setLiveRoomId(String liveRoomId) {
        this.liveRoomId = liveRoomId;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Override
    public String toString() {
        return "RedpacketCoverQuery{" +
                "liveRoomId='" + liveRoomId + '\'' +
                ", uid='" + uid + '\'' +
                '}';
    }
}
