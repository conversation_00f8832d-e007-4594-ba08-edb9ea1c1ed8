package com.yupaopao.xxq.draw.guess.dto;

import com.yupaopao.platform.common.annotation.Description;
import com.yupaopao.xxq.draw.guess.constant.GuessIssueStateEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/6/9 10:21 上午
 */
@Data
public class GuessInfoDTO implements Serializable {
    private static final long serialVersionUID = -5166448972487978979L;

    @Description("id")
    private Long guessId;

    @Description("标题")
    private String title;

    @Description("左选项")
    private OptionDTO leftOption;

    @Description("右选项")
    private OptionDTO rightOption;

    /**
     * @see GuessIssueStateEnum
     */
    @Description("状态")
    private String state;

    @Description("投注截止时间")
    private Date deadline;

    @Description("发起时间")
    private Date createTime;

    @Description("主播uid")
    private Long anchorUid;
}
