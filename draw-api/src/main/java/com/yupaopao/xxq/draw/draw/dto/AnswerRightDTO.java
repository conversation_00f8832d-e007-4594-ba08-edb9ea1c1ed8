package com.yupaopao.xxq.draw.draw.dto;

import java.io.Serializable;

/**
 * @author: liuchuan
 */
public class AnswerRightDTO implements Serializable {

    private Long uid;

    private String userId;

    private String username;

    private String avatar;

    private String rewardIcon;

    private String rewardTip;

    private RewardMsgDTO rewardDetail;

    private Boolean secret;

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getRewardIcon() {
        return rewardIcon;
    }

    public void setRewardIcon(String rewardIcon) {
        this.rewardIcon = rewardIcon;
    }

    public String getRewardTip() {
        return rewardTip;
    }

    public void setRewardTip(String rewardTip) {
        this.rewardTip = rewardTip;
    }

    public RewardMsgDTO getRewardDetail() {
        return rewardDetail;
    }

    public void setRewardDetail(RewardMsgDTO rewardDetail) {
        this.rewardDetail = rewardDetail;
    }

    public Boolean getSecret() {
        return secret;
    }

    public void setSecret(Boolean secret) {
        this.secret = secret;
    }
}
