package com.yupaopao.xxq.draw.draw.request;

import com.yupaopao.platform.common.annotation.Description;
import com.yupaopao.platform.common.po.MobileContext;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: liuchuan
 */
@ToString
@Data
public class SponsorRequest implements Serializable {

    @NotNull
    @Description("直播ID")
    private Long liveId;

    @NotNull
    @Description("用户ID")
    private String userId;

    @NotNull
    @Description("用户UID")
    private Long uid;

    @NotNull
    @Description("主播ID")
    private String anchorId;

    @NotNull
    @Description("主播UID")
    private Long anchorUid;

    @NotNull
    @Description("accessToken")
    private String accessToken;

    @Description("风控id（高额消费限制）")
    private String riskTraceId;

    @Description("mobileContext")
    private MobileContext mobileContext;
}
