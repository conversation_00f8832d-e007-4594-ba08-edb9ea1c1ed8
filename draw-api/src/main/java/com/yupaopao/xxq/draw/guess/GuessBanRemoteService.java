package com.yupaopao.xxq.draw.guess;

import com.yupaopao.platform.common.annotation.executor.CommonExecutor;
import com.yupaopao.platform.common.dto.PageResult;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.xxq.draw.guess.dto.GuessBanDTO;
import com.yupaopao.xxq.draw.guess.request.GuessBanPageRequest;
import com.yupaopao.xxq.draw.guess.request.GuessBanRequest;
import com.yupaopao.xxq.draw.guess.request.GuessBanUpdateRequest;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

public interface GuessBanRemoteService {

    @CommonExecutor(desc = "分页查询", printParam = true)
    Response<PageResult<GuessBanDTO>> findPage(@NotNull @Valid GuessBanPageRequest req);
}

