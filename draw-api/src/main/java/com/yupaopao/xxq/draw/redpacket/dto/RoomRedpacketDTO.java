package com.yupaopao.xxq.draw.redpacket.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/21 4:26 下午
 */
@Data
public class RoomRedpacketDTO implements Serializable {

    /**
     * 基本信息
     */
    private RedpacketBaseDTO baseInfo;

    /**
     * 已领取数
     */
    private Integer receivedCount;

    /**
     * 已领取列表
     */
    private List<RedpacketReceiverDTO> receiverList = new ArrayList<>();

    /**
     * 用户领取信息
     */
    private RedpacketReceiverDTO userReceiverInfo;

    /**
     * 0可抢 1已结束(已抢或者已抢完) 2不可抢
     */
    private Integer userStatus = UserStatus.CAN_RECEIVE;

    /**
     * 不可抢原因
     */
    private String noAccessReason;

    /**
     * 粉丝团拉新信息
     */
    private FansClubAttraction fansClubAttraction;

    /**
     * 用户抢红包风控拒绝
     */
    private Boolean userRiskReject;

    public RoomRedpacketDTO(Integer userStatus, String noAccessReason) {
        this.userStatus = userStatus;
        this.noAccessReason = noAccessReason;
    }

    public RoomRedpacketDTO() {
    }

    public static class UserStatus{
        /**
         * 可抢
         */
        public static final Integer CAN_RECEIVE = 0;
        /**
         * 已结束
         */
        public static final Integer END = 1;
        /**
         * 不可抢
         */
        public static final Integer NO_ACCESS = 2;
    }

    @Data
    public static class FansClubAttraction implements Serializable{
        private static final long serialVersionUID = 3533798503677092712L;

        private String desc;

        public FansClubAttraction(String desc) {
            this.desc = desc;
        }
    }
}
