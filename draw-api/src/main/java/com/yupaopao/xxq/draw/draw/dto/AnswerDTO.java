package com.yupaopao.xxq.draw.draw.dto;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;

import java.io.Serializable;

/**
 * 答题返回DTO对象
 *
 * @author: liuchuan
 */
@Data
public class AnswerDTO implements Serializable {

    @Description("是否答对")
    private Boolean answerRight;

    @Description("加密处理后的答案文本")
    private String encryptedAnswer;

    public AnswerDTO(Boolean answerRight, String encryptedAnswer) {
        this.answerRight = answerRight;
        this.encryptedAnswer = encryptedAnswer;
    }


}
