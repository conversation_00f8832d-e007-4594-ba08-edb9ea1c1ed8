package com.yupaopao.xxq.draw.redpacket.dto;

import java.io.Serializable;

public class RedpacketCoverDTO implements Serializable {
    /**
     * 红包封面 表示
     */
    private String id;

    private String name;
    /**
     * true=默认封面
     */
    private Boolean aDefault =false;
    /**
     * 红包封面字段
     */
    private String pendantGif;
    private String dialogBackground;
    private String dialogUpCover;
    private String dialogUpCoverError;
    private String dialogUnderCover;
    private String dialogButton;
    private String dialogButtonGif;
    private String buttonColor;

    private String expireTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Boolean getaDefault() {
        return aDefault;
    }

    public void setaDefault(Boolean aDefault) {
        this.aDefault = aDefault;
    }

    public String getDialogBackground() {
        return dialogBackground;
    }

    public void setDialogBackground(String dialogBackground) {
        this.dialogBackground = dialogBackground;
    }

    public String getDialogUpCover() {
        return dialogUpCover;
    }

    public void setDialogUpCover(String dialogUpCover) {
        this.dialogUpCover = dialogUpCover;
    }

    public String getDialogUpCoverError() {
        return dialogUpCoverError;
    }

    public void setDialogUpCoverError(String dialogUpCoverError) {
        this.dialogUpCoverError = dialogUpCoverError;
    }

    public String getDialogUnderCover() {
        return dialogUnderCover;
    }

    public void setDialogUnderCover(String dialogUnderCover) {
        this.dialogUnderCover = dialogUnderCover;
    }

    public String getDialogButton() {
        return dialogButton;
    }

    public void setDialogButton(String dialogButton) {
        this.dialogButton = dialogButton;
    }

    public String getDialogButtonGif() {
        return dialogButtonGif;
    }

    public void setDialogButtonGif(String dialogButtonGif) {
        this.dialogButtonGif = dialogButtonGif;
    }

    public String getButtonColor() {
        return buttonColor;
    }

    public void setButtonColor(String buttonColor) {
        this.buttonColor = buttonColor;
    }

    public String getPendantGif() {
        return pendantGif;
    }

    public void setPendantGif(String pendantGif) {
        this.pendantGif = pendantGif;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(String expireTime) {
        this.expireTime = expireTime;
    }
}
