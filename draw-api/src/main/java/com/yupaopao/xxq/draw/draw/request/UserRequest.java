package com.yupaopao.xxq.draw.draw.request;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: liuchuan
 */
@Data
public class UserRequest implements Serializable {

    @NotNull
    @Description("用户ID")
    private String userId;

    @NotNull
    @Description("用户UID")
    private Long uid;

}
