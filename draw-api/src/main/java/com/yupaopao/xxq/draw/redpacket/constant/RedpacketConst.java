package com.yupaopao.xxq.draw.redpacket.constant;

/**
 * <AUTHOR>
 * @date 2019/12/19 3:54 下午
 */
public interface RedpacketConst {

    enum RedpacketConditionType {
        /**
         * 无条件
         */
        NONE(0),
        /**
         * 关注主播
         */
        FOLLOW(1),
        /**
         * 发送弹幕
         */
        BARRAGE(2),
        /**
         * 粉丝团
         */
        FANS_CLUB(3),

        /**
         * 粉丝团等级
         */
        FANS_CLUB_LEVEL(4);

        private int code;

        RedpacketConditionType(int code) {
            this.code = code;
        }

        public int getCode() {
            return code;
        }

        public static RedpacketConditionType getInstance(String name) {
            for (RedpacketConditionType value : values()) {
                if (value.name().equalsIgnoreCase(name)) {
                    return value;
                }
            }
            return null;
        }

        public static RedpacketConditionType getInstance(Integer code) {
            for (RedpacketConditionType value : values()) {
                if (value.code == code) {
                    return value;
                }
            }
            return null;
        }
    }

    enum RedpacketStatus {
        /**
         * 在队列中
         */
        IN_QUEUE(10),
        /**
         * 已触发
         */
        TRIGGERED(20),
        /**
         * 已领完
         */
        END(30),
        /**
         * 已退款
         */
        REFUND(40);

        public boolean isEnd() {
            return END.equals(this) || REFUND.equals(this);
        }

        public static RedpacketStatus getInstance(String name) {
            for (RedpacketStatus value : values()) {
                if (value.name().equalsIgnoreCase(name)) {
                    return value;
                }
            }
            return END;
        }

        private int code;

        RedpacketStatus(int code) {
            this.code = code;
        }

        public int getCode() {
            return code;
        }
    }
}
