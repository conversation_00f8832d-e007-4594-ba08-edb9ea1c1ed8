package com.yupaopao.xxq.draw.draw.request;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * @author: liuchuan
 */
@ToString
@Data
public class StartDrawRequest implements Serializable {

    @Description("用户UID")
    private Long uid;

    @Description("通道开关")
    private Integer channelSwitch = 1;

    @Deprecated
    @Description("直播ID")
    private Long liveId;

    @Deprecated
    @Description("用户ID")
    private String userId;

}
