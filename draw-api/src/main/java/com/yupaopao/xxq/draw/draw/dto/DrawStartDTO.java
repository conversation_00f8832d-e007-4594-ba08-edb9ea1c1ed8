package com.yupaopao.xxq.draw.draw.dto;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;

import java.io.Serializable;

/**
 * 主播开始你画我猜返回DTO
 *
 * @author: liuchuan
 */
@Data
public class DrawStartDTO implements Serializable {

    @Description("你画我猜ID")
    private Long drawId;

    @Description("答案")
    private String answer;

    @Description("答案描述")
    private String answerDesc;

    @Description("答案字数长度")
    private Integer answerLength;

    @Description("奖励人数")
    private Integer awardTop;

    @Description("答对人数")
    private Integer rightCount;

    @Description("准备时间")
    private Integer readyDuration;

    @Description("游戏时间")
    private Integer gameDuration;

    @Description("赞助类型")
    private Integer sponsorType;

    @Description("赞助人UID")
    private String sponsorUid;

    @Description("赞助人名称")
    private String sponsorName;

    @Description("赞助人是否神秘人")
    private Boolean sponsorSecret;

    @Description("游戏通道")
    private Integer channelSwitch;

}
