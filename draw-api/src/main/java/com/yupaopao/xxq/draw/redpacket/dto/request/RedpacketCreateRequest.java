package com.yupaopao.xxq.draw.redpacket.dto.request;

import com.yupaopao.platform.common.annotation.Description;
import com.yupaopao.platform.common.po.MobileContext;
import com.yupaopao.xxq.draw.redpacket.dto.base.ClientInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/12/19 3:25 下午
 */
@Data
public class RedpacketCreateRequest implements Serializable {

    /**
     * 用户id
     */
    private Long uid;

    /**
     * 直播间号
     */
    private Long liveRoomId;

    /**
     * 总星钻数
     */
    private Long amount;

    /**
     * 红包数
     */
    private Integer count;

    /**
     * 红包条件类型  红包类型 0 NONE; 1 FOLLOW; 2 BARRAGE
     */
    private String conditionType;

    /**
     * 条件参数
     */
    private String conditionValue;

    /**
     * 设备信息
     */
    private ClientInfo clientInfo;

    /**
     * 其他参数
     */
    private Map<String, Object> ext;
    /**
     * 红包封面id
     */
    private String coverId;

    /**
     * 风控id（高额消费限制）
     */
    private String riskTraceId;

    @Description("上下文")
    private MobileContext mobileContext;
}
