
package com.yupaopao.xxq.game.enums;

public enum NewMultiGameState {
    NONE(0, "空态"),
    CHOSEN(1, "sud未唤醒游戏状态"),
    READY(2, "待开始"),
    PLAYING(3, "游戏中"),
    ;

    private int code;
    private String name;

    NewMultiGameState(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static NewMultiGameState getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (NewMultiGameState value : NewMultiGameState.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }

    /**
     * 用 name 获取
     */
    public static NewMultiGameState getByName(String name) {
        if (name == null) {
            return null;
        }
        for (NewMultiGameState value : NewMultiGameState.values()) {
            if (value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 是否开始游戏状态
     * @param code 参数
     * @return 返回
     */
    public static boolean isStarting(Integer code) {
        if (code == null) {
            return false;
        }
        NewMultiGameState newMultiGameState = getByCode(code);
        return newMultiGameState == READY || newMultiGameState == PLAYING;
    }
}