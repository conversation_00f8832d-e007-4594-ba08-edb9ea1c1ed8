package com.yupaopao.xxq.game.dto;

import com.yupaopao.xxq.flappyboke.dto.LabelDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by CaoZhongSheng on 2021/8/11
 */
@Data
public class SimpleUserInfoDTO implements Serializable {
    /**
     * 用户id
     */
    private String uid;
    /**
     * 短ID
     */
    private String showNo;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 性别
     */
    private Integer gender;
    /**
     * 等级
     */
    private Integer level;
    /**
     * 等级url
     */
    private String levelIcon;

    /**
     * 是否在直播
     */
    private Boolean liveStatus;

    /**
     * 直播间ID
     */
    private String liveRoomId;

    private List<LabelDTO> labelList;
}
