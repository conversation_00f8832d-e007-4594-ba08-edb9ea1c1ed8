package com.yupaopao.xxq.game.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: liuchuan
 */
@Data
public class GameRuleDTO implements Serializable {

    private String scene;

    private Long defaultFee;

    private Boolean modifyFee;

    private String gameMode;

    private Integer freeChance;

    private Integer minAnchorLevel;

    private Integer joinLimit;

    private String ruleUrl;

    private String readyUrl;

    private String goUrl;

}
