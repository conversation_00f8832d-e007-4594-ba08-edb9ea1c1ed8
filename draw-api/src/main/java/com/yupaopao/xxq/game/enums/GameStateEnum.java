package com.yupaopao.xxq.game.enums;


/**
 * 游戏状态
 * create by ji<PERSON><PERSON> on 2021/4/15
 *
 * <AUTHOR>
 **/
public enum GameStateEnum {

    IDLE(0, "空闲"),

    GAMING(1, "游戏中"),

    ;

    private Integer status;

    private String desc;

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    GameStateEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static boolean isIdle(Integer status){
        return IDLE.getStatus().equals(status);
    }

}
