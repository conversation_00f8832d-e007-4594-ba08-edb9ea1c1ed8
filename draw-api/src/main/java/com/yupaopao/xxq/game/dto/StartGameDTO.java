package com.yupaopao.xxq.game.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 游戏开始返回信息
 *
 * @author: liuchuan
 */
@Data
public class StartGameDTO implements Serializable {

    private Long gameId;

    /**
     * 赞助数
     */
    private Integer bubbleCount;

    private Long uid;

    /**
     * @see com.yupaopao.xxq.game.enums.FeeTypeEnum
     * 付费类型 0 免费 1 付费
     */
    private Integer feeType;

    private String bizExt1;

    /**
     * 系统赠送次数
     */
    private Integer systemCount;


    public static StartGameDTO convert(Long gameId, Integer bubbleCount, Long uid) {
        StartGameDTO startGameDTO = new StartGameDTO();
        startGameDTO.setGameId(gameId);
        startGameDTO.setBubbleCount(bubbleCount);
        startGameDTO.setUid(uid);
        return startGameDTO;
    }

}
