package com.yupaopao.xxq.game.dto;

import com.yupaopao.xxq.flappyboke.dto.LabelDTO;

import java.util.List;

public class BaseUserInfoDTO {

    private String username;

    private Integer gender;

    private String avatar;

    private String uid;

    private List<LabelDTO> labelList;


    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public List<LabelDTO> getLabelList() {
        return labelList;
    }

    public void setLabelList(List<LabelDTO> labelList) {
        this.labelList = labelList;
    }

    @Override
    public String toString() {
        return "BaseListUserInfoDTO{" +
                "username='" + username + '\'' +
                ", gender=" + gender +
                ", avatar='" + avatar + '\'' +
                ", uid='" + uid + '\'' +
                ", labelList=" + labelList +
                '}';
    }
}
