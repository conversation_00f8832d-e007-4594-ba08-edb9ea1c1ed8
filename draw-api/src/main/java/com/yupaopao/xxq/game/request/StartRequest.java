package com.yupaopao.xxq.game.request;

import com.yupaopao.platform.common.po.MobileContext;
import lombok.Data;

import java.io.Serializable;

/**
 *
 */
@Data
public class StartRequest implements Serializable {

    private Integer appId;

    private Long liveRoomId;

    private String scene;

    private Long uid;

    /**
     * 用于兜底异常情况
     * 主播游戏状态 0 未开始 1 正在进行
     */
    private Integer gameState;

    private MobileContext mobileContext;

}
