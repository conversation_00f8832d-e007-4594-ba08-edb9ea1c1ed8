package com.yupaopao.xxq.game.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 游戏盒子
 *
 * <AUTHOR>
 * @date 2020/1/11
 */
@Deprecated
@Data
public class GameDTO implements Serializable {

    /**
     * 图标
     */
    private String icon;

    /**
     * 跳转连接
     */
    private String schema;

    /**
     * 游戏名称
     */
    private String name;

    /**
     * 游戏类型
     */
    private int gameType;

    /**
     * 直播类型
     */
    private String liveType;

    /**
     * app版本
     */
    private String appVersion;

    /**
     * app ID
     */
    private Integer platform;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 环境
     */
    private String env;

    /**
     * 用户类型 1：主播 2：观众
     */
    private String userType;

    /**
     * 类目ID
     */
    private String categoryId;

    /**
     * 排序字段
     */
    private Integer score;

    private String iconPC;

    private Map pcIcons;
}
