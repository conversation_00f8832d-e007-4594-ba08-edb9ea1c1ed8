package com.yupaopao.xxq.game.dto;

import com.yupaopao.xxq.flappyboke.dto.LabelDTO;

import java.io.Serializable;
import java.util.List;

public class SponsorDetailDTO implements Serializable {

    /**
     * 0未开始；1已挑战
     */
    private Integer state;

    /**
     * 用户名
     */
    private String username;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 头像
     */
    private String avatar;

    /**
     * uid
     */
    private String uid;

    /**
     * 标签
     */
    private List<LabelDTO> labelList;


    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public List<LabelDTO> getLabelList() {
        return labelList;
    }

    public void setLabelList(List<LabelDTO> labelList) {
        this.labelList = labelList;
    }

    @Override
    public String toString() {
        return "SponsorDetailDTO{" +
                "state=" + state +
                ", username='" + username + '\'' +
                ", gender=" + gender +
                ", avatar='" + avatar + '\'' +
                ", uid='" + uid + '\'' +
                ", labelList=" + labelList +
                '}';
    }
}
