package com.yupaopao.xxq.game.enums;


/**
 * 付费类型
 * create by ji<PERSON><PERSON> on 2021/4/15
 *
 * <AUTHOR>
 **/
public enum  FeeTypeEnum {
    FREE(0, "免费"),

    FEE(1, "付费"),

    ;

    private Integer type;

    private String desc;

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    FeeTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static boolean isFree(Integer type){
        return FREE.getType().equals(type);
    }

    public static boolean isFee(Integer type){
        return FEE.getType().equals(type);
    }
}
