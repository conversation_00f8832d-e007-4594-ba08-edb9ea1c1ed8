package com.yupaopao.xxq.game.dto;

import com.yupaopao.xxq.interactive.dto.SudGameConfigDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: liuchuan
 */
@Data
public class GameInfoDTO implements Serializable {

    private Long id;

    private String scene;

    private String name;

    private String icon;

    private String pcIcon;

    private String pcHoverIcon;

    private String svgaUrl;

    private String pcSvgaUrl;

    private Integer svgaSort;

    private String scheme;

    private String uatScheme;

    private List<Long> topCategory;

    private List<Integer> liveType;

    private Integer sequence;

    private Integer state;

    private String appVersion;

    private String pcVersion;

    private String bxVersion;

    private String yuerAppVersion;

    private String yuerPcVersion;

    private String mvpVersion;

    private Boolean isGray;

    private List<Integer> userType;

    private String ext;

    private Boolean isSupportRound;

    private String tagImg;

    /**
     * 分类ID
     * 展示列表分类
     */
    @Deprecated
    private Integer catId;

    private Integer displayId;

    private String effectiveTime;

    private String gameSupplier;

    private SudGameConfigDTO sudGameConfig;


}
