package com.yupaopao.xxq.adventure.req;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Getter
@Setter
public class AdventureCellQueryReq implements Serializable {


    private static final long serialVersionUID = 1132794945924159173L;
    /**
     * 格子类型
     * 1: 付费格子 2: 奖励格子
     * {@link com.yupaopao.xxq.adventure.constant.AdventureCellTypeEnum}
     */
    private Integer cellType;

    /**
     * 格子内容
     */
    private String cellContent;

    /**
     * 0全部 1娱乐 2声控 3游戏 4陪玩
     * {@link com.yupaopao.xxq.adventure.constant.AdventureCategoryTypeEnum}
     */
    private Integer categoryType;

    @NotNull
    private Integer offset;
    @NotNull
    private Integer limit;

}
