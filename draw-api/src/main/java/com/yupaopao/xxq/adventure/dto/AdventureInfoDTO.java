package com.yupaopao.xxq.adventure.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class AdventureInfoDTO implements Serializable {

    private Long liveRoomId;

    private Long anchorUid;

    private Long fee = 1000L;

    private Integer queueSize = 0;

    private Boolean isInQueue = false;

    private Integer queueIndex = 0;

    private List<Long> queueList = new ArrayList<>();

    private Boolean hasPermission = false;

    private Integer playDuration = 60;


}
