package com.yupaopao.xxq.adventure.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 大冒险活动引导信息
 */
@Data
public class AdventureGuideDTO implements Serializable {

    /**
     * 用户uid
     */
    private Long uid;

    /**
     * 是否是主播
     */
    private Boolean isAnchor;

    /**
     * 是否第一次进入
     */
    private Boolean isFirstEntry;

    /**
     * 是否是新人
     */
    private Boolean isNewComer;

    /**
     * 弹窗任务奖励
     */
    private TaskInfoDTO popUp;


}
