package com.yupaopao.xxq.adventure.req;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class AdventureCellAddReq implements Serializable {


    private static final long serialVersionUID = 4096442273506054924L;
    /**
     * 格子类型
     * 1: 付费格子 2: 奖励格子
     * {@link com.yupaopao.xxq.adventure.constant.AdventureCellTypeEnum}
     */
    @NotNull
    private Integer cellType;

    /**
     * 礼物id
     */
    private String giftId;

    /**
     * 礼物数量 默认1
     */
    private Integer giftCount = 1;

    /**
     * 格子内容
     */
    private String cellContent;

    /**
     * {@link com.yupaopao.xxq.adventure.constant.AdventureCategoryTypeEnum}
     */
    private List<Integer> categoryTypeList;
}
