package com.yupaopao.xxq.adventure.constant;

import java.util.Objects;

public enum AdventureCellTypeEnum {


    REWARD_CELL(0, "奖励任务"),

    GIFT_CELL(1, "礼物任务"),

    PAY_CELL(2, "其他付费任务"),
    ;

    private Integer type;

    private String desc;

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    AdventureCellTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static boolean whether(Integer type) {
        if (Objects.isNull(type)) {
            return false;
        }
        for (AdventureCellTypeEnum typeEnum : values()) {
            if (typeEnum.getType().equals(type)) {
                return true;
            }
        }
        return false;
    }
}
