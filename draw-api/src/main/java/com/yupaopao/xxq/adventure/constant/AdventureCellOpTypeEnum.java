package com.yupaopao.xxq.adventure.constant;

import java.util.Objects;

public enum AdventureCellOpTypeEnum {

    ALL(0, "全部"),

    PAY_CELL(1, "付费"),

    REWARD_CELL(2, "奖励"),
    ;

    private Integer type;

    private String desc;

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    AdventureCellOpTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static boolean whether(Integer type) {
        if (Objects.isNull(type)) {
            return false;
        }
        for (AdventureCellOpTypeEnum typeEnum : values()) {
            if (typeEnum.getType().equals(type)) {
                return true;
            }
        }
        return false;
    }
}
