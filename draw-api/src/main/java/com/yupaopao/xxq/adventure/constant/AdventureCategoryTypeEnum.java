package com.yupaopao.xxq.adventure.constant;

import com.yupaopao.live.consts.CategoryConstant.Id;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/6 4:56 下午
 */
public enum AdventureCategoryTypeEnum {

    /**
     * 全部
     */
    ALL(0, 0),
    /**
     * 鱼雷
     */
    ENTERTAINMENT(1, Id.ENTERTAINMENT),
    /**
     * 语音
     */
    VOICE(2, Id.VOICE),
    /**
     * 游戏
     */
    GAME(3, Id.GAME),
    /**
     * 陪玩
     */
    ACCOMPANY(4, Id.ACCOMPANY);


    AdventureCategoryTypeEnum(int value, long categoryId) {
        this.value = value;
        this.categoryId = categoryId;
    }

    private int value;

    private long categoryId;

    public int getValue() {
        return value;
    }

    public long getCategoryId() {
        return categoryId;
    }

    public static Long getCategoryId(int value) {
        for (AdventureCategoryTypeEnum typeEnum : values()) {
            if (typeEnum.value == value) {
                return typeEnum.categoryId;
            }
        }
        return 0L;
    }

    public static AdventureCategoryTypeEnum getByCategoryId(Long categoryId) {
        for (AdventureCategoryTypeEnum typeEnum : values()) {
            if (typeEnum.categoryId == categoryId) {
                return typeEnum;
            }
        }
        return ALL;
    }

    /**
     * 获取十进制结果
     *
     * @return
     */
    public static Integer getDecimalByBinary(List<Integer> list) {
        Integer result = 0;
        if (CollectionUtils.isEmpty(list) || list.contains(ALL.value)) {
            return 1;
        }

        for (Integer value : list) {
            result += Double.valueOf(Math.pow(2, value)).intValue();
        }

        return result;
    }

    public static List<Integer> convertToCategoryTypeList(Integer decimalValue) {
        String binary = Integer.toBinaryString(decimalValue);
        String[] split = binary.split("");
        ArrayUtils.reverse(split);
        List<Integer> resultList = new ArrayList<>();
        for (int i = 0; i < split.length; i++) {
            if ("1".equalsIgnoreCase(split[i])) {
                resultList.add(i);
                if (i == 0) {
                    return resultList;
                }
            }
        }
        return resultList;
    }

}
