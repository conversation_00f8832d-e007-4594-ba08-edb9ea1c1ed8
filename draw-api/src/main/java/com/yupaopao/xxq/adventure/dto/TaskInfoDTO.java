package com.yupaopao.xxq.adventure.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: liuchuan
 */
@Data
public class TaskInfoDTO implements Serializable {
    private Long uid;
    private Long taskId;
    private Long uniqueTaskId;
    /**
     * 业务幂等
     */
    private String bizId;
    /**
     * 当前任务状态
     */
    private Integer status;
    /**
     * 任务领取时间
     */
    private Date acquireTime;
    /**
     * 任务达成时间
     */
    private Date finishTime;
    /**
     * 发奖励时间
     */
    private Date rewardTime;
    /**
     * 任务过期时间
     */
    private Date expireTime;

    /**
     * 所属场景id
     */
    private Long scene;
    /**
     * 任务类型
     */
    private Integer taskType;
    /**
     * 单次任务有效期
     */
    private Integer validDuration;
    /**
     * 时间单位
     */
    private String timeUnit;
    /**
     * 所属系列id
     */
    private String seriesId;
    /**
     * 所属系列排序
     */
    private Integer seriesSort;
    /**
     * 任务名
     */
    private String taskName;
    /**
     * 任务描述
     */
    private String description;
    /**
     * 任务详情
     */
    private String detail;
    /**
     * 跳转链接
     */
    private String scheme;
    /**
     * 跳转链接标题
     */
    private String schemeTitle;
    /**
     * 任务图标
     */
    private String icon;
    /**
     * 展示顺序
     */
    private Integer displaySort;
    /**
     * 可完成总次数
     */
    private Integer limitCount;
    /**
     * 完成条件id
     */
    private String finishRule;
    /**
     * 配置扩展信息
     */
    private String configExt;
    /**
     * 冷却时间 单位秒
     */
    private Integer coolingDown;
    /**
     * 此有效期可完成次数 0 = 无限制
     */
    private Integer validCount;
    /**
     * 此有效期内已结束次数 仅当查询当前有效期时有值
     */
    private Integer endCount;
    /**
     * 奖励id
     */
    private Long rewardId;
    /**
     * 任务领取类型
     **/
    private Integer acquireType;
    /**
     * 任务奖励类型
     **/
    private Integer rewardType;

    /**
     * 任务来源
     */
    private String source;

    /**
     * 行为进度列表
     */
    private List<TaskProgressDTO> progresses = new ArrayList<>();

}