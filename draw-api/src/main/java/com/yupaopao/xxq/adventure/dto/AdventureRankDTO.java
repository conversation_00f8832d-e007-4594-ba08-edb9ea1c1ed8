package com.yupaopao.xxq.adventure.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: liuchuan
 */
@Data
public class AdventureRankDTO implements Serializable {

    private Long liveRoomId;

    private String uid;

    private String avatar;

    private String anchorLevelIcon;

    private String username;

    private String liveStatus;

    private Boolean adventuring = false;

    private Long score;

    private Integer age;

    private Integer gender;

    private Integer rank;


}
