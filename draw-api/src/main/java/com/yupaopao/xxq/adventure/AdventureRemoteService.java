package com.yupaopao.xxq.adventure;

import com.yupaopao.platform.common.annotation.executor.CommonExecutor;
import com.yupaopao.platform.common.dto.PageResult;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.platform.common.po.Device;
import com.yupaopao.xxq.CatPoint;
import com.yupaopao.xxq.adventure.dto.AdventureInfoDTO;
import com.yupaopao.xxq.adventure.dto.CustomCellDTO;
import com.yupaopao.xxq.adventure.dto.MapInfoDTO;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 大冒险格子服务
 *
 * <AUTHOR>
 */
public interface AdventureRemoteService {

    @CommonExecutor(desc = "主播修改报名费", printParam = true, printResponse = true)
    Response<Boolean> updateFee(@NotNull Long uid, @NotNull Long fee);

    @CatPoint(event = "Adventure.apply")
    @CommonExecutor(desc = "报名大冒险", printParam = true, printResponse = true)
    Response<Integer> apply(@NotNull Long uid, @NotNull Long liveRoomId, @NotNull String accessToken);

    @CommonExecutor(desc = "取消大冒险", printParam = true, printResponse = true)
    Response<Boolean> cancel(@NotNull Long liveRoomId, @NotNull Long uid);

    @CommonExecutor(desc = "主播忽略大冒险排队用户", printParam = true, printResponse = true)
    Response<Boolean> ignore(@NotNull Long liveRoomId, @NotNull Long uid);

    @CatPoint(event = "Adventure.start")
    @CommonExecutor(desc = "开始大冒险", printParam = true, printResponse = true)
    Response<Boolean> start(@NotNull Long uid, @NotNull Device device);

    @CatPoint(event = "Adventure.startV2")
    @CommonExecutor(desc = "开始大冒险v2", printParam = true, printResponse = true)
    Response<Boolean> startV2(@NotNull Long anchorUid, @NotNull Device device, @NotNull Long uid);

    @CatPoint(event = "Adventure.play")
    @CommonExecutor(desc = "play大冒险", printParam = true, printResponse = true)
    Response<Integer> play(@NotNull Long adventureId, @NotNull Long uid);

    @CatPoint(event = "Adventure.end")
    @CommonExecutor(desc = "结束大冒险", printParam = true, printResponse = true)
    Response<Boolean> end(@NotNull Long adventureId);

    @CommonExecutor(desc = "创建自定义格子", printParam = true, printResponse = true)
    Response<Boolean> createCustomCell(@NotNull Long uid, @NotNull String name, @NotNull Device device);

    @CommonExecutor(desc = "修改自定义格子", printParam = true, printResponse = true)
    Response<Boolean> updateCustomCell(@NotNull Long cellId, @NotNull String name, @NotNull Device device);

    @CommonExecutor(desc = "查询格子列表", printParam = true, printResponse = true)
    Response<List<CustomCellDTO>> findAllCell(@NotNull Long uid);

    @CommonExecutor(desc = "删除格子", printParam = true, printResponse = true)
    Response<Boolean> deleteCustomCell(@NotNull Long cellId);

    @CommonExecutor(desc = "大冒险面板信息", printParam = true, printResponse = true)
    Response<AdventureInfoDTO> info(@NotNull Long liveRoomId, Long uid);

    @CommonExecutor(desc = "查询当前大冒险信息", printParam = true, printResponse = true)
    Response<MapInfoDTO> getCurrentAdventure(@NotNull Long liveRoomId);

    @CommonExecutor(desc = "排队列表", printParam = true, printResponse = true)
    Response<PageResult<Long>> findQueueList(@NotNull Long liveRoomId, String anchor, Integer limit);


}
