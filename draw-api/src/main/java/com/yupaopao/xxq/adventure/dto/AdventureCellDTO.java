package com.yupaopao.xxq.adventure.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AdventureCellDTO implements Serializable {

    private static final long serialVersionUID = -5241008547970197491L;
    /**
     * 格子id
     */
    private Long cellId;

    /**
     * 格子内容
     */
    private String cellContent;

    /**
     * 格子类型
     */
    private Integer cellType;

    /**
     * 礼物id
     */
    private GiftDTO giftDTO;

    /**
     * 礼物数量
     */
    private Integer giftCount;

    /**
     * 类目列表
     */
    private List<Integer> categoryTypeList;
}
