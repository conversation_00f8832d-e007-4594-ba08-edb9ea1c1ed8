package com.yupaopao.xxq.adventure;

import com.yupaopao.platform.common.annotation.executor.CommonExecutor;
import com.yupaopao.platform.common.dto.PageResult;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.xxq.adventure.dto.AdventureCellDTO;
import com.yupaopao.xxq.adventure.req.AdventureCellAddReq;
import com.yupaopao.xxq.adventure.req.AdventureCellQueryReq;
import com.yupaopao.xxq.adventure.req.AdventureCellUpdateReq;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 大冒险格子服务
 *
 * <AUTHOR>
 */
public interface AdventureCellRemoteService {

    @CommonExecutor(desc = "大冒险格子内容列表", printParam = true, printResponse = true)
    Response<PageResult<AdventureCellDTO>> findAdventureCellList(@NotNull @Valid AdventureCellQueryReq req);

    @CommonExecutor(desc = "添加格子", printParam = true, printResponse = true)
    Response<Long> addCell(@NotNull AdventureCellAddReq req);

    @CommonExecutor(desc = "修改格子", printParam = true, printResponse = true)
    Response<Void> updateCell(@NotNull AdventureCellUpdateReq req);

    @CommonExecutor(desc = "删除格子", printParam = true, printResponse = true)
    Response<Void> delCell(@NotNull Long cellId);
}
