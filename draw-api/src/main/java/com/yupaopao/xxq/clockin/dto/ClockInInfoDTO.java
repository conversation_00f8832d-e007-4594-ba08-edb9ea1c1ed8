package com.yupaopao.xxq.clockin.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 跟播打卡
 **/
@Data
public class ClockInInfoDTO implements Serializable {

    /**
     * 累计打卡天数
     */
    private Integer totalDays;

    /**
     * 补卡券数量
     */
    private Integer ticketAmount;

    /**
     * 日历
     */
    private List<ClockInCalendarDTO> calendarList;

    /**
     * 自动打卡倒计时
     */
    private Integer countdown;

    /**
     * 奖品列表
     */
    private List<ClockInPrizeDTO> prizeList;

}
