package com.yupaopao.xxq.clockin;

import com.yupaopao.platform.common.dto.PageResult;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.xxq.clockin.dto.ClockInInfoDTO;
import com.yupaopao.xxq.clockin.dto.ClockInRankDTO;
import com.yupaopao.xxq.clockin.dto.ClockInRecordDTO;
import com.yupaopao.xxq.clockin.request.ClockInSendTicketRequest;

import java.util.List;

/**
 * <AUTHOR>
 **/
public interface ClockInRemoteService {

    /**
     * 获取今日打卡信息
     * @param uid
     * @param anchorUid
     * @return
     */
    Response<ClockInInfoDTO> getTodayInfo(Long uid, Long anchorUid);

    /**
     * 获取主播今日打卡记录
     * @param anchorUid
     * @param cursor
     * @param size
     * @return
     */
    Response<PageResult<ClockInRecordDTO>> getTodayRecordList(Long anchorUid, String cursor, Integer size);

    /**
     * 补打卡
     * @param uid
     * @param anchorUid
     * @param signDate
     * @return
     */
    Response<Boolean> mend(Long uid, Long anchorUid, String signDate);

    /**
     * 获取榜单列表
     * @param anchorUid
     * @return
     */
    Response<PageResult<ClockInRankDTO>> getRankList(Long anchorUid);

    /**
     * 获取
     * @param anchorUid
     * @param uid
     * @return
     */
    Response<ClockInRankDTO> getSingleRank(Long anchorUid, Long uid);

    /**
     * 发放补卡券
     * @param uid
     * @param sendType
     * @param amount
     * @param fansClubId
     * @return
     */
    Response<Boolean> sendTicket(Long uid, Integer sendType, Integer amount, Long fansClubId);

    /**
     * 批量发放补卡券
     * @param requestList
     * @return
     */
    Response<Boolean> sendTicketBatch(List<ClockInSendTicketRequest> requestList);

}
