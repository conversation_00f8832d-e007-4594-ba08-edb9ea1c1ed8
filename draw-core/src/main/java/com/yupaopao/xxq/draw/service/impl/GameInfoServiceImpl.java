package com.yupaopao.xxq.draw.service.impl;
import java.util.Date;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.live.dto.LiveDTO;
import com.yupaopao.live.enums.CategoryEnums;
import com.yupaopao.live.enums.ClientTypeEnum;
import com.yupaopao.live.enums.LiveGameEnum;
import com.yupaopao.platform.common.constant.APP;
import com.yupaopao.platform.common.constant.OS;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.platform.common.po.Client;
import com.yupaopao.platform.common.po.MobileContext;
import com.yupaopao.platform.common.po.User;
import com.yupaopao.platform.common.utils.VersionUtil;
import com.yupaopao.xxq.draw.config.DrawApolloConfig;
import com.yupaopao.xxq.draw.config.SudApolloConfig;
import com.yupaopao.xxq.draw.consts.ClientConsts;
import com.yupaopao.xxq.draw.convert.GameConvert;
import com.yupaopao.xxq.draw.domain.GameDO;
import com.yupaopao.xxq.draw.domain.MultiGameConfigDO;
import com.yupaopao.xxq.draw.dto.GameSceneCountItem;
import com.yupaopao.xxq.draw.dto.GameVersionConfig;
import com.yupaopao.xxq.draw.exception.ErrorCode;
import com.yupaopao.xxq.draw.manager.LiveManager;
import com.yupaopao.xxq.draw.mapper.GameMapper;
import com.yupaopao.xxq.draw.service.GameInfoService;
import com.yupaopao.xxq.draw.service.cache.GameCache;
import com.yupaopao.xxq.draw.service.impl.interactive.MultiGameService;
import com.yupaopao.xxq.draw.util.DateUtil;
import com.yupaopao.xxq.game.dto.GameInfoDTO;
import com.yupaopao.xxq.game.enums.NewMultiGameState;
import com.yupaopao.xxq.interactive.dto.SudGameConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.yupaopao.xxq.draw.util.StringUtil.encodeUrl;
import static com.yupaopao.xxq.draw.util.StringUtil.replaceTemplate;

/**
 * @author: liuchuan
 */
@Slf4j
@Service
public class GameInfoServiceImpl implements GameInfoService {

    @Resource
    private GameMapper gameMapper;

    @Resource
    private GameCache gameCache;

    @Resource
    private DrawApolloConfig drawApolloConfig;

    @Resource
    private SudApolloConfig sudApolloConfig;

    @Resource
    private RedisService redisService;

    @Resource
    private MultiGameService multiGameService;

    public static final String SUD_GAME_ROOM_COUNT_LIST = "sud.game.room.count.list";
    @Resource
    private LiveManager liveManager;

    @Override
    public List<GameInfoDTO> findGames(LiveDTO liveDTO, Long topCategoryId, Integer userType, MobileContext mc) {
        List<GameDO> list = gameCache.getAllWithWhitelistFilter(mc.getUid());
        List<GameInfoDTO> gameInfoDTOList = GameConvert.convertDTO(list);
        if (CollectionUtils.isEmpty(gameInfoDTOList)) {
            return new ArrayList<>();
        }
        if (liveDTO.getClientType() == null) {
            log.warn("findGames: liveDTO clientType is null, return empty list");
            return new ArrayList<>();
        }
        log.info("game_list_data, list1:{}, liveDTO:{}, topCategoryId:{},userType:{}", JSONObject.toJSONString(gameInfoDTOList), JSONObject.toJSONString(liveDTO), topCategoryId, userType);
        // 如果是轮播房就挑选出支持轮播房的游戏玩法
        if (liveDTO.isRound()) {
            gameInfoDTOList = gameInfoDTOList.stream().filter(GameInfoDTO::getIsSupportRound).collect(Collectors.toList());
        }
        // 填充SUD游戏配置
        fillSudConfig(gameInfoDTOList, liveDTO.getLiveRoomId());
        // 筛选用户、主播
        gameInfoDTOList = gameInfoDTOList.stream().filter(a -> CollectionUtils.isEmpty(a.getUserType()) || a.getUserType().contains(userType)).collect(Collectors.toList());
        // 筛选类目
        gameInfoDTOList = gameInfoDTOList.stream().filter(a -> CollectionUtils.isEmpty(a.getTopCategory()) || a.getTopCategory().contains(topCategoryId)).collect(Collectors.toList());
        // 筛选展示时间
        gameInfoDTOList = gameInfoDTOList.stream().filter(a -> inEffectiveTime(a.getEffectiveTime())).collect(Collectors.toList());
        // 筛选各端版本
        String client = convertClient(mc.getAppId(), mc.getOsName());
        gameInfoDTOList = gameInfoDTOList.stream().filter(a -> checkVersion(client, mc.getClient().getVersion(), a)).collect(Collectors.toList());
        gameInfoDTOList = versionFilter(gameInfoDTOList, topCategoryId, userType, mc.getClient().getVersion(), client);
        gameInfoDTOList = categoryFilter(gameInfoDTOList, liveDTO.getCategoryId());
        if (CollectionUtils.isEmpty(gameInfoDTOList)) {
            return Lists.newArrayList();
        }
        // 处理scheme
        encodeScheme(gameInfoDTOList, liveDTO.getHostLiveRoomId() == null ? liveDTO.getLiveRoomId() : liveDTO.getHostLiveRoomId());
        // 语音连麦入口兼容
        filterVoiceLink(gameInfoDTOList, mc, topCategoryId, client);
        // 互动小游戏过滤
        filterSudGame(gameInfoDTOList, liveDTO);
        return gameInfoDTOList;
    }


    @Override
    public List<GameInfoDTO> findMultiGame(LiveDTO liveDTO, Long topCategoryId, MobileContext mc) {
        List<GameDO> list = gameCache.getAllWithWhitelistFilter(mc.getUid());
        List<GameInfoDTO> gameInfoDTOList = GameConvert.convertDTO(list);
        if (CollectionUtils.isEmpty(gameInfoDTOList)) {
            return new ArrayList<>();
        }

        // 筛选出所有的多人sud游戏
        gameInfoDTOList = gameInfoDTOList.stream()
                .filter(GameInfoServiceImpl::ifSudGame)
                .collect(Collectors.toList());
        // 如果是轮播房就挑选出支持轮播房的游戏玩法
        if (liveDTO.isRound()) {
            gameInfoDTOList = gameInfoDTOList.stream().filter(GameInfoDTO::getIsSupportRound).collect(Collectors.toList());
        }
        // 填充SUD游戏配置
        fillSudConfig(gameInfoDTOList, liveDTO.getLiveRoomId());
        // 筛选类目
        gameInfoDTOList = gameInfoDTOList.stream().filter(a -> CollectionUtils.isEmpty(a.getTopCategory()) || a.getTopCategory().contains(topCategoryId)).collect(Collectors.toList());
        // 筛选展示时间
        gameInfoDTOList = gameInfoDTOList.stream().filter(a -> inEffectiveTime(a.getEffectiveTime())).collect(Collectors.toList());
        // 筛选各端版本
        String client = convertClient(mc.getAppId(), mc.getOsName());
        gameInfoDTOList = gameInfoDTOList.stream().filter(a -> checkVersion(client, mc.getClient().getVersion(), a)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(gameInfoDTOList)) {
            return Lists.newArrayList();
        }
        return gameInfoDTOList;
    }

    private static boolean ifSudGame(GameInfoDTO a) {
        // SUD 游戏
        if ("SUD".equalsIgnoreCase(a.getGameSupplier()) && a.getScene().startsWith("SUD_MULTI")){
            return true;
        }
        // SUG 游戏
        if ("SUG".equalsIgnoreCase(a.getGameSupplier()) && a.getScene().startsWith("SUG_")){
            return true;
        }
        return false;
    }

    @Override
    public List<GameInfoDTO> findAllSudGames(Long uid) {
        List<GameDO> list = gameCache.getAllWithWhitelistFilter(uid);
        List<GameInfoDTO> gameInfoDTOList = GameConvert.convertDTO(list);
        if (CollectionUtils.isEmpty(gameInfoDTOList)) {
            return new ArrayList<>();
        }

        // 筛选出所有的多人sud游戏
        return gameInfoDTOList.stream().filter(GameInfoServiceImpl::ifSudGame).collect(Collectors.toList());
    }

    @Override
    public boolean hasGameRight(String scene, LiveDTO liveDTO, Integer userType, Long uid, Integer appId, Integer productId, String appVersion, String osName) {
        MobileContext mobileContext = new MobileContext();
        Client client = new Client();
        client.setApp(APP.getAppByCode(appId));
        client.setOs(OS.getOs(osName));
        client.setVersion(appVersion);
        client.setProductId(productId);
        mobileContext.setClient(client);
        mobileContext.setUser(new User(null, uid, null));
        try {
            List<GameInfoDTO> gameInfoDTOList = findGames(liveDTO, liveDTO.getTopCategoryId(), userType, mobileContext);
            if (CollectionUtils.isEmpty(gameInfoDTOList)) {
                return false;
            }
            GameInfoDTO game = gameInfoDTOList.stream().filter(a -> a.getScene().equalsIgnoreCase(scene)).findFirst().orElse(null);
            return game != null;
        } catch (Exception e) {
            log.error("GameInfoService.hasGameRight error, scene:{}, mc:{}, liveDTO:{}, userType:{}", scene, JSONObject.toJSONString(mobileContext), JSONObject.toJSONString(liveDTO), userType, e);
            return false;
        }
    }

    @Override
    public boolean hasMultiGameRight(String scene, LiveDTO liveDTO, Long uid, Integer appId, Integer productId, String appVersion, String osName) {
        MobileContext mobileContext = new MobileContext();
        Client client = new Client();
        client.setApp(APP.getAppByCode(appId));
        client.setOs(OS.getOs(osName));
        client.setVersion(appVersion);
        client.setProductId(productId);
        mobileContext.setClient(client);
        mobileContext.setUser(new User(null, uid, null));
        try {
            List<GameInfoDTO> gameInfoDTOList = findMultiGame(liveDTO, liveDTO.getTopCategoryId(), mobileContext);
            if (CollectionUtils.isEmpty(gameInfoDTOList)) {
                return false;
            }
            GameInfoDTO game = gameInfoDTOList.stream().filter(a -> a.getScene().equalsIgnoreCase(scene)).findFirst().orElse(null);
            return game != null;
        } catch (Exception e) {
            log.error("GameInfoService.hasGameRight error, scene:{}, mc:{}, liveDTO:{}", scene, JSONObject.toJSONString(mobileContext), JSONObject.toJSONString(liveDTO), e);
            return false;
        }
    }

    @Override
    public GameInfoDTO getByScene(String scene, @Nullable Long liveRoomId) {
        GameDO gameDO = gameCache.getByScene(scene);
        GameInfoDTO dto = GameConvert.convertDTO(gameDO);
        if (dto != null) {
            fillSudConfig(Collections.singletonList(dto), liveRoomId);
        }
        return dto;
    }

    @Override
    public Response<Boolean> createGame(GameInfoDTO gameInfoDTO) {
        GameDO gameDO = GameConvert.convertDO(gameInfoDTO);
        if (gameInfoDTO == null) {
            return Response.toast("参数不正确");
        }
        return Response.success(gameMapper.insert(gameDO) > 0);
    }

    @Override
    public Response<Boolean> updateGame(GameInfoDTO gameInfoDTO) {
        if (gameMapper.getByScene(gameInfoDTO.getScene()) == null) {
            return Response.fail(ErrorCode.GAME_NOT_EXITS);
        }
        GameDO gameDO = GameConvert.convertDO(gameInfoDTO);
        return Response.success(gameMapper.update(gameDO) > 0);
    }

    @Override
    public List<GameInfoDTO> findAll() {
        return GameConvert.convertDTO(gameMapper.findAll());
    }

    /**
     * game_config 全表扫描，计算热度值
     */
    @Override
    public void gameSceneCountJobRun() {
        long anchor = 0L;
        Map<String, GameSceneCountItem> gameSceneCountMap = new HashMap<>();
        while(true) {
            // 查询游戏中房间
            List<MultiGameConfigDO> configDOS = multiGameService.queryStartingRoomByPage(anchor, 20);
            if (CollectionUtils.isEmpty(configDOS)) {
                log.info("gameSceneCount configDOS is empty, anchor: {}", anchor);
                break;
            }
            for (MultiGameConfigDO config : configDOS) {
                anchor = Math.max(anchor, config.getId());
                String gameScene = config.getGameScene();
                if (StringUtils.isEmpty(gameScene)) {
                    continue;
                }
                // 根据游戏类型对房间计数
                GameSceneCountItem item = gameSceneCountMap.computeIfAbsent(gameScene, x -> {
                    GameSceneCountItem countItem = new GameSceneCountItem();
                    countItem.setGameScene(x);
                    countItem.setTotal(0);
                    countItem.setReady(0);
                    countItem.setPlaying(0);
                    return countItem;
                });
                // total 计数
                item.setTotal(item.getTotal() + 1);
                if (Objects.equals(NewMultiGameState.READY.getCode(), config.getNewGameState())) {
                    // ready 计数
                    item.setReady(item.getReady() + 1);
                }
                else if (Objects.equals(NewMultiGameState.PLAYING.getCode(), config.getNewGameState())) {
                    // playing计数
                    item.setPlaying(item.getPlaying() + 1);
                }
            }
            log.info("gameSceneCount configDOS size: {}, anchor: {}", configDOS.size(), anchor);
        }
        List<GameSceneCountItem> items = gameSceneCountMap.entrySet().stream()
                .sorted((o1, o2) -> o2.getValue().getTotal().compareTo(o1.getValue().getTotal()))
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());
        redisService.set(SUD_GAME_ROOM_COUNT_LIST, JSONObject.toJSONString(items));
    }

    @Override
    public List<GameSceneCountItem> queryGameSceneResult() {
        String str = (String) redisService.get(SUD_GAME_ROOM_COUNT_LIST);
        if (StringUtils.isEmpty(str)) {
            return new ArrayList<>();
        }
        return JSONObject.parseArray(str, GameSceneCountItem.class);
    }

    private void fillSudConfig(List<GameInfoDTO> gameInfoDTOList, @Nullable Long liveRoomId) {
        Map<String, SudGameConfigDTO> sudConfigMap = sudApolloConfig.querySudGameConfigMap(liveRoomId);
        if (CollectionUtils.isEmpty(gameInfoDTOList) || MapUtils.isEmpty(sudConfigMap)) {
            return;
        }

        gameInfoDTOList.forEach(gameInfo -> {
            SudGameConfigDTO config = sudConfigMap.get(gameInfo.getScene());
            if (config != null) {
                gameInfo.setSudGameConfig(config);
            }
        });

    }


    private void encodeScheme(List<GameInfoDTO> list, Long liveRoomId) {
        //星球夺宝礼物 拼接 liveRoomId
        list.forEach(gameDTO -> {
            if ("STAR_TREASURE".equalsIgnoreCase(gameDTO.getScene())) {
                Map<String, String> params = Maps.newHashMap();
                params.put("liveRoomId", liveRoomId.toString());
                gameDTO.setScheme(encodeUrl(replaceTemplate(gameDTO.getScheme(), params)));
            }
        });
    }

    private String convertClient(int appId, String osName) {
        // 比心
        if (appId == APP.BIXIN.getCode()) {
            return ClientConsts.BIXIN_APP;
        }

        // mvp
        if (appId == APP.MVP.getCode()) {
            return ClientConsts.MVP_APP;
        }

        // 饭糖
        if (appId == APP.FAN_TANG.getCode()) {
            return ClientConsts.FANTANG_APP;
        }

        // 游咖
        if (appId == APP.YOU_KA.getCode()) {
            return ClientConsts.YOUKA_APP;
        }

        // 鱼耳语音
        if (appId == APP.YUER.getCode()) {
            if (OS.WINDOWS.getName().equalsIgnoreCase(osName)) {
                return ClientConsts.YUER_PC;
            } else {
                return ClientConsts.YUER_APP;
            }
        }
        // 鱼耳直播
        if (appId == APP.UNIVERSE.getCode()) {
            if (OS.WINDOWS.getName().equalsIgnoreCase(osName)) {
                return ClientConsts.LIVE_PC;
            } else {
                return ClientConsts.LIVE_APP;
            }
        }
        return ClientConsts.UNKNOWN;
    }


    private void filterVoiceLink(List<GameInfoDTO> list, MobileContext mc, Long topCategoryId, String client) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        // 如果是娱乐类目,app版本需要大于等于4.5.0 pc版本必须大于等于2.5.0
        if (CategoryEnums.FirstLevel.ENTERTAINMENT.getValue().intValue() == topCategoryId) {
            if (ClientConsts.LIVE_PC.equalsIgnoreCase(client) && VersionUtil.compare("2.7.0", mc.getClient().getVersion()) > 0) {
                list.removeIf(a -> LiveGameEnum.VOICE_LINK.name().equalsIgnoreCase(a.getScene()));
            }

            if (ClientConsts.LIVE_APP.equalsIgnoreCase(client) && VersionUtil.compare("4.7.0", mc.getClient().getVersion()) > 0) {
                list.removeIf(a -> LiveGameEnum.VOICE_LINK.name().equalsIgnoreCase(a.getScene()));
            }

            if (ClientConsts.BIXIN_APP.equalsIgnoreCase(client) && VersionUtil.compare("8.2.0", mc.getClient().getVersion()) > 0) {
                list.removeIf(a -> LiveGameEnum.VOICE_LINK.name().equalsIgnoreCase(a.getScene()));
            }
        }

        // 游戏或者上分
        if (CategoryEnums.FirstLevel.GAME.getValue().intValue() == topCategoryId || CategoryEnums.FirstLevel.ACCOMPANY.getValue().intValue() == topCategoryId) {
            if (ClientConsts.LIVE_PC.equalsIgnoreCase(client) && VersionUtil.compare("7.1.0", mc.getClient().getVersion()) > 0) {
                list.removeIf(a -> LiveGameEnum.VOICE_LINK.name().equalsIgnoreCase(a.getScene()));
            }

            if (ClientConsts.LIVE_APP.equalsIgnoreCase(client) && VersionUtil.compare("9.1.0", mc.getClient().getVersion()) > 0) {
                list.removeIf(a -> LiveGameEnum.VOICE_LINK.name().equalsIgnoreCase(a.getScene()));
            }

            if (ClientConsts.BIXIN_APP.equalsIgnoreCase(client) && VersionUtil.compare("8.25.0", mc.getClient().getVersion()) > 0) {
                list.removeIf(a -> LiveGameEnum.VOICE_LINK.name().equalsIgnoreCase(a.getScene()));
            }
        }
    }

    private List<GameInfoDTO> versionFilter(List<GameInfoDTO> gameInfoDTOList, Long topCategoryId, Integer userType, String version, String client) {
        List<GameVersionConfig> versionConfigList = drawApolloConfig.queryGameVersionLimit(userType, topCategoryId);
        if (CollectionUtils.isEmpty(gameInfoDTOList) || CollectionUtils.isEmpty(versionConfigList)) {
            return gameInfoDTOList;
        }
        Map<String, GameVersionConfig> map = versionConfigList.stream().collect(Collectors.toMap(GameVersionConfig::getScene, i -> i, (k1, k2) -> k1));
        gameInfoDTOList = gameInfoDTOList.stream().filter(game -> {
            GameVersionConfig config = map.get(game.getScene());
            if (Objects.isNull(config)) {
                return true;
            }
            if (config.isHide()) {
                return false;
            }
            if (MapUtils.isEmpty(config.getVersionMap())) {
                return true;
            }
            if (StringUtils.isEmpty(config.getVersionMap().get(client))) {
                return true;
            }
            return VersionUtil.compare(version, config.getVersionMap().get(client)) >= 0;
        }).collect(Collectors.toList());
        return gameInfoDTOList;
    }

    /**
     * 根据三级品类过滤玩法，暂时只有多人连麦有场景在使用
     *
     * @param gameInfoDTOList
     * @param categoryId
     * @return
     */
    private List<GameInfoDTO> categoryFilter(List<GameInfoDTO> gameInfoDTOList, Long categoryId) {
        Map<String, List<Long>> categoryLimitMap = drawApolloConfig.queryGameCategoryLimit();
        if (CollectionUtils.isEmpty(gameInfoDTOList) || MapUtils.isEmpty(categoryLimitMap)) {
            return gameInfoDTOList;
        }
        gameInfoDTOList = gameInfoDTOList.stream().filter(game -> {
            List<Long> categoryList = categoryLimitMap.get(game.getScene());
            if (CollectionUtils.isEmpty(categoryList)) {
                return true;
            }
            return categoryList.contains(categoryId);
        }).collect(Collectors.toList());
        return gameInfoDTOList;
    }


    private boolean inEffectiveTime(String effectiveTime) {
        if (StringUtils.isEmpty(effectiveTime)) {
            return true;
        }

        try {
            List<String> timeList = JSONObject.parseArray(effectiveTime, String.class);
            if (CollectionUtils.isEmpty(timeList) || timeList.size() != 2) {
                return false;
            }

            Date start = DateUtil.parseDate(timeList.get(0));
            Date end = DateUtil.parseDate(timeList.get(1));
            if (start == null || end == null || start.after(end)) {
                return false;
            }

            Date now = new Date();
            return now.after(start) && now.before(end);

        } catch (Exception e) {
            log.error("GameInfoService.detectEffectiveTime error, effectiveTime={}", effectiveTime, e);
            return false;
        }
    }


    private boolean checkVersion(String client, String appVersion, GameInfoDTO game) {
        if (ClientConsts.BIXIN_APP.equalsIgnoreCase(client)) {
            return StringUtils.hasText(game.getBxVersion()) && VersionUtil.compare(game.getBxVersion(), appVersion) <= 0;
        } else if (ClientConsts.YUER_APP.equalsIgnoreCase(client)) {
            return StringUtils.hasText(game.getYuerAppVersion()) && VersionUtil.compare(game.getYuerAppVersion(), appVersion) <= 0;
        } else if (ClientConsts.YUER_PC.equalsIgnoreCase(client)) {
            return StringUtils.hasText(game.getYuerPcVersion()) && VersionUtil.compare(game.getYuerPcVersion(), appVersion) <= 0;
        } else if (ClientConsts.LIVE_APP.equalsIgnoreCase(client)) {
            return StringUtils.hasText(game.getAppVersion()) && VersionUtil.compare(game.getAppVersion(), appVersion) <= 0;
        } else if (ClientConsts.LIVE_PC.equalsIgnoreCase(client)) {
            return StringUtils.hasText(game.getPcVersion()) && VersionUtil.compare(game.getPcVersion(), appVersion) <= 0;
        } else if (ClientConsts.MVP_APP.equalsIgnoreCase(client)) {
            return StringUtils.hasText(game.getMvpVersion()) && VersionUtil.compare(game.getMvpVersion(), appVersion) <= 0;
        } else if (ClientConsts.FANTANG_APP.equalsIgnoreCase(client)) {
            return StringUtils.hasText(game.getAppVersion()) && VersionUtil.compare(game.getAppVersion(), appVersion) <= 0;
        } else if (ClientConsts.YOUKA_APP.equalsIgnoreCase(client)) {
            // 游咖等于直播 9.15.0 后续如果迭代再单独提需求
            return StringUtils.hasText(game.getAppVersion()) && VersionUtil.compare(game.getAppVersion(), "9.15.0") <= 0;
        }
        return false;
    }


    private void filterSudGame(List<GameInfoDTO> gameInfoDTOList, LiveDTO liveDTO) {
        if (CollectionUtils.isEmpty(gameInfoDTOList)) {
            return;
        }

        // 是否是pc开播
        boolean isPC = ClientTypeEnum.isPc(liveDTO.getClientType());
        // 是否是游戏上分品类
        boolean isGameOrCarry = CategoryEnums.FirstLevel.GAME.getValue().intValue() == liveDTO.getTopCategoryId() || CategoryEnums.FirstLevel.ACCOMPANY.getValue().intValue() == liveDTO.getTopCategoryId();
        // pc是否满足版本
        boolean supportPCVersion = supportPCVersion(liveDTO);
        // pc开播、并且属于游戏和上分品类、但是pc版本不符合则需要把多人小游戏场景下掉
        if (isPC && isGameOrCarry && !supportPCVersion) {
            removeMultiGame(gameInfoDTOList);
        }
        // 如果不是pc开播、并且是游戏上分品类，则直接移除多人小游戏
        if (!isPC && isGameOrCarry) {
            removeMultiGame(gameInfoDTOList);
        }
    }


    private boolean supportPCVersion(LiveDTO liveDTO) {
        // 是否是pc
        boolean isPC = ClientTypeEnum.isPc(liveDTO.getClientType());
        if (!isPC) {
            return false;
        }

        // pc是否满足版本
        JSONObject extJson = JSONObject.parseObject(liveDTO.getExt());
        if (extJson == null) {
            return false;
        }

        String liveDTOVersion = extJson.getString("VERSION");
        if (StringUtils.isEmpty(liveDTOVersion)) {
            return false;
        }
        return sudApolloConfig.supportPCCategoryMultiGame(ClientTypeEnum.YUER_LIVE_PC.getType(), liveDTOVersion);
    }


    /**
     * 过滤多人小游戏
     */
    private void removeMultiGame(List<GameInfoDTO> gameInfoDTOList) {
        // 如果老模式开关打开了，需要把新模式剔除
        gameInfoDTOList.removeIf(a ->
                a.getScene().startsWith("SUD_MULTI") || a.getScene().startsWith("SUG_")
        );
        // 移除"一起玩"
        gameInfoDTOList.removeIf(a -> LiveGameEnum.GAMING_TOGETHER.name().equalsIgnoreCase(a.getScene()));
    }

}
