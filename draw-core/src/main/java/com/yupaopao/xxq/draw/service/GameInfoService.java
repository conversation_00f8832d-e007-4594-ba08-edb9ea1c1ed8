package com.yupaopao.xxq.draw.service;

import com.yupaopao.live.dto.LiveDTO;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.platform.common.po.MobileContext;
import com.yupaopao.xxq.draw.dto.GameSceneCountItem;
import com.yupaopao.xxq.game.dto.GameInfoDTO;

import java.util.List;


public interface GameInfoService {


    /**
     * 游戏列表
     */
    List<GameInfoDTO> findGames(LiveDTO liveDTO, Long topCategoryId, Integer userType, MobileContext mc);


    /**
     * 查询支持的sud多人小游戏
     */
    List<GameInfoDTO> findMultiGame(LiveDTO liveDTO, Long topCategoryId, MobileContext mc);


    List<GameInfoDTO> findAllSudGames();

    /**
     * 查询所有SUD游戏，支持白名单过滤
     *
     * @param uid 用户uid，用于白名单过滤
     * @return SUD游戏列表
     */
    List<GameInfoDTO> findAllSudGames(Long uid);

    /**
     * 判断对方是否有该玩法的权利（不包括多人小游戏）
     */
    boolean hasGameRight(String scene, LiveDTO liveDTO, Integer userType, Long uid, Integer appId, Integer productId, String appVersion, String osName);


    /**
     * 判断对方的多人小游戏的权利
     */
    boolean hasMultiGameRight(String scene, LiveDTO liveDTO, Long uid, Integer appId, Integer productId, String appVersion, String osName);

    /**
     * 获取游戏详情
     *
     * @param scene
     * @param liveRoomId
     * @return
     */
    GameInfoDTO getByScene(String scene, Long liveRoomId);

    Response<Boolean> createGame(GameInfoDTO gameInfoDTO);

    Response<Boolean> updateGame(GameInfoDTO gameInfoDTO);

    List<GameInfoDTO> findAll();

    void gameSceneCountJobRun();

    List<GameSceneCountItem> queryGameSceneResult();
}
