package com.yupaopao.xxq.draw.manager;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yupaopao.live.dto.LiveDTO;
import com.yupaopao.live.enums.LiveGameEnum;
import com.yupaopao.live.enums.LiveStatusEnum;
import com.yupaopao.xxq.draw.config.SudApolloConfig;
import com.yupaopao.xxq.draw.consts.RoomTypeEnum;
import com.yupaopao.xxq.draw.domain.InteractiveGameApplyDO;
import com.yupaopao.xxq.draw.domain.MultiGameConfigDO;
import com.yupaopao.xxq.draw.domain.MultiGameSeatDO;
import com.yupaopao.xxq.draw.dto.GameSceneCountItem;
import com.yupaopao.xxq.draw.integration.rpc.GameStateRPC;
import com.yupaopao.xxq.draw.service.impl.MultiGameRoundRecordService;
import com.yupaopao.xxq.interactive.dto.SudUserExistInfo;
import com.yupaopao.xxq.draw.integration.MultiGameChatroomMsgSender;
import com.yupaopao.xxq.draw.integration.rpc.UserRelationRpc;
import com.yupaopao.xxq.draw.manager.convert.MultiGameApplyConvert;
import com.yupaopao.xxq.draw.manager.convert.MultiGameConfigConvert;
import com.yupaopao.xxq.draw.manager.convert.MultiGameSeatConvert;
import com.yupaopao.xxq.draw.service.GameInfoService;
import com.yupaopao.xxq.draw.service.impl.interactive.InteractiveGameApplyService;
import com.yupaopao.xxq.draw.service.impl.interactive.MultiGameSeatService;
import com.yupaopao.xxq.draw.service.impl.interactive.MultiGameService;
import com.yupaopao.xxq.game.dto.AnchorSudGameStatusDto;
import com.yupaopao.xxq.game.dto.GameInfoDTO;
import com.yupaopao.xxq.game.dto.MatchGameDTO;
import com.yupaopao.xxq.game.enums.NewMultiGameState;
import com.yupaopao.xxq.interactive.constant.GameTypeEnum;
import com.yupaopao.xxq.interactive.dto.*;
import com.yupaopao.xxq.interactive.request.MultiGameAnchorInviteQueryRequest;
import com.yupaopao.xxq.interactive.request.MultiGameApplyQueryRequest;
import com.yupaopao.xxq.interactive.request.MultiGameUserApplyQueryRequest;
import com.yupaopao.xxq.user.response.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 多人小游戏查询 manager
 * @date 2023/7/27 17:05
 */
@Component
@Slf4j
public class MultiGameQueryManager {

    public static final String SUD_MULTI_GOBANG = "SUD_MULTI_GOBANG";
    @Resource
    private SudApolloConfig sudApolloConfig;

    @Resource
    private MultiGameCommonManager multiGameCommonManager;

    @Resource
    private InteractiveGameApplyService interactiveGameApplyService;
    @Resource
    private MultiGameService multiGameService;
    @Resource
    private MultiGameSeatService multiGameSeatService;

    @Resource
    private LiveManager liveManager;

    @Resource
    private GameInfoService gameInfoService;

    @Resource
    private UserManager userManager;

    @Resource
    private UserRelationRpc userRelationRpc;

    @Resource
    private MultiGameChatroomMsgSender multiGameChatroomMsgSender;

    @Resource
    private MultiGameRoundRecordService multiGameRoundRecordService;

    @Resource
    private GameStateRPC gameStateRPC;

    public MultiGameApplyPageList findApplyWaitList(MultiGameApplyQueryRequest request) {
        MultiGameApplyPageList pageList = new MultiGameApplyPageList();
        List<MultiGameApplyDTO> resultList = Lists.newArrayList();
        pageList.setMultiGameApplyDTOList(resultList);
        List<InteractiveGameApplyDO> list = interactiveGameApplyService.queryWaitPageList(request.getScene(),
                request.getLiveRoomId(),
                Arrays.asList(request.getType()),
                request.getAnchor(),
                request.getLimit());
        if (CollectionUtils.isEmpty(list)) {
            return pageList;
        }
        list.forEach(i -> resultList.add(MultiGameApplyConvert.convert(i)));
        return pageList;
    }

    public MultiGameUserApplyPageList findUserApplyWaitList(MultiGameUserApplyQueryRequest request) {
        MultiGameUserApplyPageList pageList = new MultiGameUserApplyPageList();
        List<MultiGameApplyDTO> resultList = Lists.newArrayList();
        pageList.setMultiGameApplyDTOList(resultList);
        List<InteractiveGameApplyDO> list = interactiveGameApplyService.queryWaitPageList(null,
                request.getLiveRoomId(),
                Arrays.asList(GameTypeEnum.USER_APPLY.getType(), GameTypeEnum.USER_SUD_ROOM.getType()),
                request.getAnchor(),
                request.getLimit());
        if (CollectionUtils.isEmpty(list)) {
            return pageList;
        }
        list.forEach(i -> resultList.add(MultiGameApplyConvert.convert(i)));
        return pageList;
    }

    public MultiGameAnchorInvitePageList findAnchorInviteWaitList(MultiGameAnchorInviteQueryRequest request) {
        MultiGameAnchorInvitePageList pageList = new MultiGameAnchorInvitePageList();
        List<MultiGameApplyDTO> resultList = Lists.newArrayList();
        pageList.setMultiGameApplyDTOList(resultList);
        List<InteractiveGameApplyDO> list = interactiveGameApplyService.queryWaitPageListDesc(null, request.getLiveRoomId(), GameTypeEnum.ANCHOR_INVITE.getType(), request.getAnchor(), request.getLimit());
        if (CollectionUtils.isEmpty(list)) {
            return pageList;
        }
        list.forEach(i -> resultList.add(MultiGameApplyConvert.convert(i)));
        return pageList;
    }

    /**
     * 查询申请信息
     *
     * @param request
     * @return
     */
    public MultiGameApplyDTO findApplyByUid(MultiGameApplyQueryRequest request) {
        InteractiveGameApplyDO applyDO = interactiveGameApplyService.findWaitApplyInfo(request.getLiveRoomId(), request.getUid(), request.getScene(), request.getType());
        if (Objects.isNull(applyDO)) {
            return null;
        }
        return MultiGameApplyConvert.convert(applyDO);
    }

    public MultiGameSeatInfoDTO findSeatInfoByLiveRoomId(Long liveRoomId) {
        // 查询游戏配置信息
        MultiGameConfigDO multiGameConfig = multiGameService.findMultiGameConfig(liveRoomId);
        if (Objects.isNull(multiGameConfig)) {
            return null;
        }
        if (StringUtils.isEmpty(multiGameConfig.getGameScene())) {
            String gameScene = multiGameRoundRecordService.selectMaxGameScene(multiGameConfig.getAnchorUid());
            if (StringUtils.isEmpty(gameScene)) {
                gameScene = SUD_MULTI_GOBANG;
            }
            multiGameService.chooseGameScene(multiGameConfig, gameScene);
            multiGameConfig = multiGameService.findMultiGameConfig(liveRoomId);
            multiGameConfig.setGameScene(gameScene);
        }

        MultiGameConfigDTO multiGameConfigDTO = MultiGameConfigConvert.convert(multiGameConfig);
        Map<String, SudGameConfigDTO> sudGameConfigDTOMap = sudApolloConfig.querySudGameConfigMap(liveRoomId);
        multiGameConfigDTO.setSudGameConfig(sudGameConfigDTOMap.get(multiGameConfig.getGameScene()));

        // 查询麦位信息
        List<MultiGameSeatDO> gameSeatList = multiGameSeatService.findGameSeatList(liveRoomId);
        List<GameSeatInfoDTO> gameSeatInfoDTOList = gameSeatList.stream()
                .map(MultiGameSeatConvert::convert)
                .collect(Collectors.toList());

        MultiGameSeatInfoDTO multiGameSeatInfoDTO = new MultiGameSeatInfoDTO();
        multiGameSeatInfoDTO.setLiveRoomId(liveRoomId);
        multiGameSeatInfoDTO.setGameConfig(multiGameConfigDTO);
        multiGameSeatInfoDTO.setGameSeatInfoList(gameSeatInfoDTOList);
        return multiGameSeatInfoDTO;
    }

    /**
     * 查询用户麦位信息
     *
     * @param liveRoomId
     * @param uid
     * @return
     */
    public GameSeatInfoDTO findSeatInfoByUid(Long liveRoomId, Long uid) {
        MultiGameSeatDO gameSeatDO = multiGameSeatService.findSeatByUid(liveRoomId, uid);
        if (Objects.isNull(gameSeatDO)) {
            return null;
        }
        return MultiGameSeatConvert.convert(gameSeatDO);
    }

    public AnchorSudGameStatusDto queryAnchorSudGameStatus(Long anchorUid) {
        AnchorSudGameStatusDto res = new AnchorSudGameStatusDto();
        LiveDTO liveDTO = liveManager.getLivingByUid(anchorUid);
        if (liveDTO == null) {
            log.warn("queryAnchorSudGameStatus liveDTO is null, anchorUid:{}", anchorUid);
            return null;
        }
        MultiGameConfigDO multiGameConfig = multiGameService.findMultiGameConfig(liveDTO.getLiveRoomId());
        if (multiGameConfig == null) {
            log.warn("queryAnchorSudGameStatus multiGameConfig is null, anchorUid:{}", anchorUid);
            return null;
        }

        res.setAnchorUid(anchorUid);
        res.setLiveRoomId(liveDTO.getLiveRoomId());
        res.setStatus(multiGameConfig.getNewGameState());
        res.setScene(multiGameConfig.getGameScene());
        return res;
    }

    public List<MatchGameDTO> queryMatchGameList(Long uid) {
        // 查询直播间内的 sud games（进行白名单过滤）
        Map<String, GameInfoDTO> allSudGamesMap = gameInfoService.findAllSudGames(uid).stream()
                .collect(Collectors.toMap(GameInfoDTO::getScene, x -> x, (x1, x2) -> x1));
        // 查询匹配页面的 sud game list 配置，按照这个顺序作为返回顺序
        List<MatchGameDTO> matchGames = sudApolloConfig.querySudMatchGameConfig().stream().map(x -> {
            // 如果直播间内无配置，直接过滤
            GameInfoDTO gameInfoDTO = allSudGamesMap.get(x.getScene());
            if (gameInfoDTO == null) {
                return null;
            }
            MatchGameDTO gameDTO = new MatchGameDTO();
            gameDTO.setGameScene(x.getScene());
            gameDTO.setImage(x.getImage());
            gameDTO.setHot(false);
            gameDTO.setName(gameInfoDTO.getName());
            return gameDTO;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        // 设置热门标识
        Map<String, MatchGameDTO> gameConfigDTOMap = matchGames.stream()
                .collect(Collectors.toMap(MatchGameDTO::getGameScene, x -> x, (x1, x2) -> (x1)));
        List<GameSceneCountItem> countItems = gameInfoService.queryGameSceneResult();
        log.info("queryMatchGameList countItems:{}", countItems);
        int hotCount = 0;
        for (GameSceneCountItem countItem : countItems) {
            if (countItem.getTotal() <= 0) {
                continue;
            }
            MatchGameDTO gameDTO = gameConfigDTOMap.get(countItem.getGameScene());
            if (gameDTO != null) {
                gameDTO.setHot(true);
                hotCount++;
            }
            // 仅限设置两个 hot
            if (hotCount >= 2) {
                break;
            }
        }
        return matchGames;
    }

    public List<MatchGameDTO> popularList() {
        return queryMatchGameList().stream().filter(MatchGameDTO::getHot).collect(Collectors.toList());
    }

    public Boolean queryMatchSwitchInfo(Long liveRoomId) {
        return multiGameService.queryMatchSwitchInfo(liveRoomId);
    }

    public List<MatchUserInfo> matchUserList(Long liveRoomId, Integer appId) {
        LiveDTO liveDTO = liveManager.getLiveRoomInfo(liveRoomId);
        List<MultiGameSeatDO> gameSeatList = multiGameSeatService.findGameSeatList(liveRoomId);
        List<MultiGameSeatDO> sudRoomSeats = gameSeatList.stream()
                .filter(x -> RoomTypeEnum.GAME_ROOM.getRoomType().equals(x.getSource()))
                .collect(Collectors.toList());
        List<Long> uids = sudRoomSeats.stream().map(MultiGameSeatDO::getUid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(uids)) {
            return new ArrayList<>();
        }
        // 查询用户信息
        Map<Long, UserInfo> userInfoMap = userManager.roomUserMap(uids, liveRoomId);
        Map<Long, Boolean> followedMap = userRelationRpc.isFollowedByTargets(liveDTO.getUid(), uids, appId);

        List<MatchUserInfo> matchUserInfos = sudRoomSeats.stream()
                .map(x -> {
                    UserInfo userInfo = userInfoMap.get(x.getUid());
                    if (userInfo == null) {
                        return null;
                    }
                    MatchUserInfo matchUserInfo = new MatchUserInfo();
                    matchUserInfo.setUid(x.getUid());
                    matchUserInfo.setUsername(userInfo.getUsername());
                    matchUserInfo.setGender(userInfo.getGender());
                    matchUserInfo.setAvatar(userInfo.getAvatar());

                    Boolean followed = followedMap.get(userInfo.getUid());
                    matchUserInfo.setFollowYou(Boolean.TRUE.equals(followed));
                    matchUserInfo.setSecret(userInfo.getSecret());
                    return matchUserInfo;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return matchUserInfos;
    }

    public List<SudMatchAnchorStatusDto> queryBatchSudMatchAnchorStatus(List<Long> anchorUids) {
        Map<Long, LiveDTO> liveDTOMap = liveManager.getLivingByUidList(anchorUids).stream()
                .collect(Collectors.toMap(LiveDTO::getUid, x -> x, (x1, x2) -> x1));

        List<Long> liveRoomIds = liveDTOMap.values().stream().map(LiveDTO::getLiveRoomId).collect(Collectors.toList());
        Map<Long, MultiGameConfigDO> multiGameConfigDOMap = multiGameService.findMultiGameConfigs(liveRoomIds).stream()
                        .collect(Collectors.toMap(MultiGameConfigDO::getAnchorUid, x -> x, (x1, x2) -> x1));

        Map<Long, Set<Integer>> stateSetMap = gameStateRPC.batchGetStateSetAll(liveRoomIds);

        return anchorUids.stream().map(x -> {
            SudMatchAnchorStatusDto res = new SudMatchAnchorStatusDto();
            res.setAnchorUid(x);

            LiveDTO liveDTO = liveDTOMap.get(x);
            if (liveDTO == null || !LiveStatusEnum.LIVING.name().equals(liveDTO.getStatus())) {
                return null;
            }

            MultiGameConfigDO multiGameConfig = multiGameConfigDOMap.get(x);
            // 发送sud房间匹配消息
            if (multiGameConfig == null) {
                log.warn("querySudMatchAnchorStatus multiGameConfig is null, anchorUid:{}", x);
                return null;
            }

            Set<Integer> set = stateSetMap.get(liveDTO.getLiveRoomId());
            // 没有冲突状态，才可以进行匹配
            if (!CollectionUtils.isEmpty(set)) {
                set.retainAll(Arrays.asList(LiveGameEnum.VOICE_LINK.getScene(),
                        LiveGameEnum.ADVENTURE.getScene(),
                        LiveGameEnum.DRAW.getScene(),
                        LiveGameEnum.NEW_FACE_LINK.getScene(),
                        LiveGameEnum.MULTI_LINK.getScene(),
                        LiveGameEnum.SUD_WAR_OF_KINGS.getScene()));
                if (!CollectionUtils.isEmpty(set)) {
                    log.info("querySudMatchAnchorStatus game conflicted anchorUid:{}, liveRoomId:{}, set: {}", x, liveDTO.getLiveRoomId(), set);
                    return null;
                }
            }

            boolean started = !StringUtils.isEmpty(multiGameConfig.getGameScene())
                    && NewMultiGameState.isStarting(multiGameConfig.getNewGameState());
            if (!started && multiGameConfig.getMatchSwitch() == 0) {
                log.info("querySudMatchAnchorStatus matchSwitch is off, anchorUid:{}", x);
                return null;
            }

            List<String> gameScenes = sudApolloConfig.querySudMatchGameConfig().stream().map(SudMatchGameConfigDTO::getScene).collect(Collectors.toList());
            // 不是匹配页游戏不发匹配消息
            if (!gameScenes.contains(multiGameConfig.getGameScene())) {
                log.warn("querySudMatchAnchorStatus gameScene 不是匹配页游戏, anchorUid:{}, config: {}", x, JSON.toJSONString(multiGameConfig));
                return null;
            }
            res.setGameStatus(multiGameConfig.getNewGameState());
            res.setGameScene(multiGameConfig.getGameScene());

            // 这边只build，复用下里面的逻辑，并不会发送消息
            Map<String, Object> map = multiGameChatroomMsgSender.buildMatchKafkaMap(
                    multiGameConfig.getAnchorUid(),
                    multiGameConfig.getUsedSeatCount(),
                    multiGameConfig.getMatchSwitch() == 1,
                    multiGameConfig.getNewGameState(),
                    multiGameConfig.getGameScene(),
                    null,
                    liveDTO.getLiveRoomId());
            res.setHasSudAnchorIdentity((Boolean) map.get("hasSudAnchorIdentity"));
            res.setHasGameSeat((Boolean) map.get("hasGameSeat"));
            res.setHasLinkSeat((Boolean) map.get("hasLinkSeat"));
            return res;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public SudUserExistInfo getSudUserExist(Long uid) {
        return multiGameService.getSudUserExist(uid);
    }
}
