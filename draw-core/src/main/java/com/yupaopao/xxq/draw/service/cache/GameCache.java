package com.yupaopao.xxq.draw.service.cache;


import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.yupaopao.xxq.draw.config.DrawApolloConfig;
import com.yupaopao.xxq.draw.domain.GameDO;
import com.yupaopao.xxq.draw.mapper.GameMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 游戏缓存
 *
 * @author: liuchuan
 */
@Component
@Slf4j
public class GameCache {

    @Resource
    private GameMapper gameMapper;

    @Resource
    private DrawApolloConfig drawApolloConfig;

    private final static String KEY = "UNIQUE";

    private LoadingCache<String, List<GameDO>> cache;

    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        cache = Caffeine.newBuilder()
                .maximumSize(10)
                .expireAfterWrite(10, TimeUnit.SECONDS)
                .build(this::build);
    }

    private List<GameDO> build(String key) {
        return getAll();
    }


    public List<GameDO> getAll() {
        List<GameDO> list = cache.getIfPresent(KEY);
        if (!CollectionUtils.isEmpty(list)) {
            return list;
        }

        list = getGamesByEnv();
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        list.sort(Comparator.comparing(GameDO::getSequence));
        cache.put(KEY, list);

        return list;
    }

    public GameDO getByScene(String scene) {
        List<GameDO> list = getAllWithWhitelistFilter(null);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().filter(a -> scene.equals(a.getScene())).findFirst().orElse(null);
    }

    /**
     * 根据环境获取所有游戏
     *
     * @return
     */
    private List<GameDO> getGamesByEnv() {
        List<GameDO> games = gameMapper.findAllVaild();
        if (CollectionUtils.isEmpty(games)) {
            return new ArrayList<>();
        }
        // 非灰度环境过滤掉灰度展示的游戏
        if (drawApolloConfig.notGray()) {
            games = games.stream().filter(a -> !a.getIsGray()).collect(Collectors.toList());
        }
        // 灰度环境把uatScheme配置到scheme属性
        if (drawApolloConfig.isGray()) {
            games.forEach(a -> a.setScheme(a.getUatScheme()));
        }
        return games;
    }

    /**
     * 获取所有游戏，不进行白名单过滤
     *
     * @return 所有游戏列表
     */
    public List<GameDO> getAllWithWhitelistFilter() {
        return getAll();
    }

    /**
     * 获取所有游戏，并根据用户uid进行白名单过滤
     *
     * @param uid 用户uid，如果为null则不进行白名单过滤
     * @return 过滤后的游戏列表
     */
    public List<GameDO> getAllWithWhitelistFilter(Long uid) {
        List<GameDO> allGames = getAll();
        if (CollectionUtils.isEmpty(allGames) || uid == null) {
            return allGames;
        }

        return allGames.stream().filter(game -> {
            // 如果游戏没有ext字段或为空，则不过滤
            if (StringUtils.isEmpty(game.getExt())) {
                return true;
            }

            try {
                // 解析ext字段
                JSONObject extJson = JSONObject.parseObject(game.getExt());
                if (extJson == null || !extJson.containsKey("whitelist")) {
                    return true;
                }

                // 获取白名单配置
                List<Long> whitelist = JSONObject.parseArray(extJson.getString("whitelist"), Long.class);
                if (CollectionUtils.isEmpty(whitelist)) {
                    return true;
                }

                // 检查当前用户是否在白名单中
                return whitelist.contains(uid);
            } catch (Exception e) {
                log.error("getAllWithWhitelistFilter parse ext error, scene: {}, ext: {}, uid: {}", game.getScene(), game.getExt(), uid, e);
                // 解析失败时默认不过滤
                return true;
            }
        }).collect(Collectors.toList());
    }

}
