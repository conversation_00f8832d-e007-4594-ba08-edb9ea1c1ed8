package com.yupaopao.xxq.draw.provider;

import com.yupaopao.arthur.sdk.mobileapi.MobileAPIContext;
import com.yupaopao.arthur.sdk.mobileapi.MobileAPIParamEnum;
import com.yupaopao.platform.common.dto.Code;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.xxq.draw.consts.MultiGameEndType;
import com.yupaopao.xxq.draw.consts.MultiGameSeatChangeType;
import com.yupaopao.xxq.interactive.dto.SudUserExistInfo;
import com.yupaopao.xxq.draw.manager.MultiGameFlowManager;
import com.yupaopao.xxq.draw.manager.MultiGameManager;
import com.yupaopao.xxq.draw.manager.MultiGameQueryManager;
import com.yupaopao.xxq.game.dto.AnchorSudGameStatusDto;
import com.yupaopao.xxq.game.dto.MatchGameDTO;
import com.yupaopao.xxq.game.request.AnchorSudGameStatusReq;
import com.yupaopao.xxq.interactive.MultiGameRemoteService;
import com.yupaopao.xxq.interactive.dto.*;
import com.yupaopao.xxq.interactive.request.*;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * create by jiaqian on 2021/7/9
 *
 * <AUTHOR>
 **/
@DubboService
public class MultiGameController implements MultiGameRemoteService {

    @Resource
    private MultiGameManager multiGameManager;

    @Resource
    private MultiGameQueryManager multiGameQueryManager;

    @Resource
    private MultiGameFlowManager multiGameFlowManager;


    @Override
    public Response<Boolean> apply(MultiGameApplyRequest request) {
        return Response.success(multiGameManager.apply(request));
    }

    @Override
    public Response<Boolean> invite(MultiGameInviteRequest request) {
        return Response.success(multiGameManager.invite(request));
    }

    @Override
    public Response<Boolean> cancel(MultiGameApplyCancelRequest request) {
        return Response.success(multiGameManager.cancel(request));
    }

    @Override
    public Response<Boolean> userDisagree(MultiGameApplyDisagreeRequest request) {
        return Response.success(multiGameManager.userDisagree(request));
    }

    @Override
    public Response<Boolean> anchorDisagree(MultiGameApplyDisagreeRequest request) {
        return Response.success(multiGameManager.anchorDisagree(request));
    }


    @Override
    public Response<Boolean> anchorAgreeApply(MultiGameApplyAgreeRequest request) {
        if (request == null || request.getGameApplyId() == null || request.getAnchorUid() == null || request.getLiveRoomId() == null) {
            return Response.fail(Code.ERROR_PARAM);
        }
        boolean success = multiGameFlowManager.agreeApply(request.getAnchorUid(), request.getLiveRoomId(), request.getGameApplyId());
        return Response.success(success);
    }

    @Override
    public Response<Boolean> userAgreeInvite(MultiGameInviteAgreeRequest request) {
        if (request == null || request.getGameApplyId() == null || request.getUid() == null || request.getLiveRoomId() == null) {
            return Response.fail(Code.ERROR_PARAM);
        }
        boolean success = multiGameFlowManager.agreeInvite(request.getUid(), request.getLiveRoomId(), request.getGameApplyId());
        return Response.success(success);
    }


    @Override
    public Response<Boolean> seatDownFromGameRoom(MultiGameSeatDownRequest request) {
        if (request == null || request.getAppId() == null || request.getUid() == null || request.getLiveRoomId() == null || StringUtils.isEmpty(request.getGameScene())) {
            return Response.fail(Code.ERROR_PARAM);
        }

        boolean success = multiGameFlowManager.seatDownFromGameRoom(request.getUid(), request.getLiveRoomId(), request.getAppId(), request.getGameScene());
        return Response.success(success);
    }

    @Override
    public Response<MultiGameRTCConnectDTO> connectRTC(MultiGameConnectRTCRequest request) {
        if (request == null || request.getAppId() == null || request.getUid() == null || request.getLiveRoomId() == null) {
            return Response.fail(Code.ERROR_PARAM);
        }
        MultiGameRTCConnectDTO dto = multiGameFlowManager.connectRTC(request.getLiveRoomId(), request.getUid(), request.getAppId());
        return Response.success(dto);
    }

    @Override
    public Response<Boolean> chooseGame(MultiGameChooseRequest request) {
        if (request == null || request.getLiveRoomId() == null || request.getAnchorUid() == null || StringUtils.isEmpty(request.getGameScene())) {
            return Response.fail(Code.ERROR_PARAM);
        }

        multiGameFlowManager.chooseGame(request.getLiveRoomId(), request.getAnchorUid(), request.getGameScene());
        return Response.success(true);
    }

    @Override
    public Response<MultiGameStartDTO> startGame(MultiGameStartRequest request) {
        if (request == null || request.getLiveRoomId() == null || request.getAnchorUid() == null || StringUtils.isEmpty(request.getGameScene())) {
            return Response.fail(Code.ERROR_PARAM);
        }
        MultiGameStartDTO dto = multiGameFlowManager.startGame(request.getLiveRoomId(), request.getAnchorUid(), request.getGameScene());
        return Response.success(dto);
    }

    @Override
    public Response<Boolean> roundStart(MultiGameRoundStartRequest request) {
        multiGameFlowManager.roundStart(request.getLiveRoomId(), request.getAnchorUid());
        return Response.success(true);
    }

    @Override
    public Response<Boolean> roundEnd(MultiGameRoundEndRequest request) {
        multiGameFlowManager.roundEnd(request.getLiveRoomId());
        return Response.success(true);
    }

    @Override
    public Response<MultiGameStartDTO> changeGame(MultiGameChangeRequest request) {
        if (request == null || request.getLiveRoomId() == null || request.getAnchorUid() == null || StringUtils.isEmpty(request.getOriginScene()) || StringUtils.isEmpty(request.getTargetScene())) {
            return Response.fail(Code.ERROR_PARAM);
        }
        MultiGameStartDTO dto = multiGameFlowManager.changeGame(request.getLiveRoomId(), request.getAnchorUid(), request.getTargetScene());
        return Response.success(dto);
    }

    @Override
    public Response<Boolean> exit(MultiGameExitRequest request) {
        if (request == null || request.getLiveRoomId() == null || request.getUid() == null) {
            return Response.fail(Code.ERROR_PARAM);
        }
        if (request.getDuration() == null) {
            request.setDuration(0);
        }
        return multiGameFlowManager.exit(request.getLiveRoomId(), request.getUid(), request.getDuration());
    }

    @Override
    public Response<Boolean> kick(MultiGameKickRequest request) {
        if (request == null || request.getLiveRoomId() == null || request.getAnchorUid() == null || request.getTargetUid() == null) {
            return Response.fail(Code.ERROR_PARAM);
        }
        return multiGameFlowManager.kick(request.getLiveRoomId(), request.getAnchorUid(), request.getTargetUid(), MultiGameSeatChangeType.SIT_KICK);
    }

    @Override
    public Response<Boolean> end(MultiGameEndRequest request) {
        if (request == null || request.getLiveRoomId() == null || request.getAnchorUid() == null) {
            return Response.fail(Code.ERROR_PARAM);
        }
        multiGameFlowManager.end(request.getLiveRoomId(), request.getAnchorUid(), MultiGameEndType.NORMAL_END);
        return Response.success(true);
    }

    @Override
    public Response<Boolean> endGameAndChange(MultiGameEndChangeRequest request) {
        multiGameFlowManager.endGameAndChange(request.getLiveRoomId(), request.getGameScene());
        return Response.success(true);
    }


    @Override
    public Response<MultiGameApplyDTO> queryApplyInfo(MultiGameApplyQueryRequest request) {
        return Response.success(multiGameQueryManager.findApplyByUid(request));
    }

    @Override
    public Response<GameSeatInfoDTO> querySeatInfo(MultiGameUserSeatQueryRequest request) {
        GameSeatInfoDTO seatInfoDTO = multiGameQueryManager.findSeatInfoByUid(request.getLiveRoomId(), request.getUid());
        return Response.success(seatInfoDTO);
    }

    @Override
    public Response<MultiGameApplyPageList> queryApplyPageList(MultiGameApplyQueryRequest request) {
        return Response.success(multiGameQueryManager.findApplyWaitList(request));
    }

    @Override
    public Response<MultiGameUserApplyPageList> queryUserApplyList(MultiGameUserApplyQueryRequest request) {
        return Response.success(multiGameQueryManager.findUserApplyWaitList(request));
    }

    @Override
    public Response<MultiGameAnchorInvitePageList> queryAnchorInviteList(MultiGameAnchorInviteQueryRequest request) {
        return Response.success(multiGameQueryManager.findAnchorInviteWaitList(request));
    }

    @Override
    public Response<MultiGameSeatInfoDTO> getGameSeatInfo(MultiGameSeatInfoRequest request) {
        MultiGameSeatInfoDTO seatInfo = multiGameQueryManager.findSeatInfoByLiveRoomId(request.getLiveRoomId());
        return Response.success(seatInfo);
    }

    @Override
    public Response<AnchorSudGameStatusDto> queryAnchorSudGameStatus(AnchorSudGameStatusReq request) {
        return Response.success(multiGameQueryManager.queryAnchorSudGameStatus(request.getAnchorUid()));
    }

    @Override
    public Response<List<SudMatchAnchorStatusDto>> queryBatchSudMatchAnchorStatus(BatchSudMatchAnchorStatusReq request) {
        return Response.success(multiGameQueryManager.queryBatchSudMatchAnchorStatus(request.getAnchorUids()));
    }

    @Override
    public Response<List<MatchGameDTO>> queryMatchGameList() {
        // 从上下文获取当前用户的 uid，用于白名单过滤
        Long uid = MobileAPIContext.get(MobileAPIParamEnum.USER_UID.getValue(), Long.class);
        return Response.success(multiGameQueryManager.queryMatchGameList(uid));
    }

    @Override
    public Response<List<MatchGameDTO>> popularList() {
        return Response.success(multiGameQueryManager.popularList());
    }

    @Override
    public Response<Boolean> queryMatchSwitchInfo(GameMatchSwitchQueryReq request) {
        return Response.success(multiGameQueryManager.queryMatchSwitchInfo(request.getLiveRoomId()));
    }

    @Override
    public Response<Boolean> matchSwitchChange(GameMatchSwitchReq request) {
        return Response.success(multiGameManager.matchSwitchChange(request.getLiveRoomId(), request.getOn()));
    }

    @Override
    public Response<List<MatchUserInfo>> matchUserList(MatchUserListReq request) {
        return Response.success(multiGameQueryManager.matchUserList(request.getLiveRoomId(), request.getAppId()));
    }

    @Override
    public Response<SudUserExistInfo> getSudUserExist(SudExistReq sudoExistReq) {
        return Response.success(multiGameQueryManager.getSudUserExist(sudoExistReq.getUid()));
    }
}
